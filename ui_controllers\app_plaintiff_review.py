import json, os
import pandas as pd
import numpy as np # Import numpy
from flask import jsonify, request, render_template, url_for # Added render_template, url_for
from DatabaseManagement.Connections import get_gz_connection
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_batch, save_df_to_feather # Keep feather if used
from Alerts.ReprocessCases import reprocess_cases 
from logdata import log_message # Import log_message

# Import cache manager module and refresh function
import cache_manager

# Import functions from Plaintiff_Clean_Up that interact with these routes
from Fixes.Plaintiff_Clean_Up import (
    improve_plaintiff_9_and_2_letter_names, # Needed for run_improve_9_and_len2
    delete_plaintiff_without_cases, # Needed for get/delete_plaintiffs_without_cases
    add_plaintiff_id, # Needed for submit_plaintiff_reviews
)

def init_plaintiff_review_routes(app):
    """Initialize routes related to plaintiff review processes."""

    # --- HTML Page Routes ---
    @app.route('/plaintiff_review')
    def plaintiff_review_page():
        """Render the page for reviewing plaintiff name suggestions."""
        # Ensure cache is loaded, no force needed just for page render
        cache_manager.refresh_cached_data(force=False)
        return render_template('plaintiff_review.html') # Assuming this template exists

    @app.route('/plaintiff_duplicates')
    def plaintiff_duplicates_page():
        """Render the page for resolving duplicate plaintiffs."""
        # Ensure cache is loaded, no force needed just for page render
        cache_manager.refresh_cached_data(force=False)
        return render_template('plaintiff_duplicates.html')

    # --- API Endpoints ---

    @app.route('/api/plaintiff/reviews', methods=['GET'])
    def get_plaintiff_reviews():
        """API endpoint to get all case plaintiff name reviews"""
        log_message("Request received for /api/plaintiff/reviews", level="DEBUG")
        force_refresh_param = request.args.get('force_refresh', 'false').lower() == 'true' # Get and convert force_refresh param
        log_message(f"force_refresh parameter: {force_refresh_param}", level="DEBUG") # Log the received parameter
        try:
            # Ensure cache is loaded, respecting force_refresh
            if not cache_manager.refresh_cached_data(force=force_refresh_param): # Use the parameter
                 log_message("Error: Cache could not be loaded in /api/plaintiff/reviews", level="ERROR")
                 return jsonify({"error": "Data cache not available"}), 500

            # Check if necessary cached data exists using cache_manager prefix
            if cache_manager.cached_plaintiff_reviews is None or cache_manager.cached_cases_df is None or cache_manager.cached_plaintiff_df is None:
                log_message("Error: Required cached dataframes are None", level="ERROR")
                return jsonify({"error": "Required data not found in cache"}), 500

            # If the reviews table is empty, return empty list
            if cache_manager.cached_plaintiff_reviews.empty:
                return jsonify({
                    'success': True,
                    'reviews': []
                })

            # Merge with cases to get docket and date_filed
            merged_df = pd.merge(
                cache_manager.cached_plaintiff_reviews,
                cache_manager.cached_cases_df[['id', 'docket', 'date_filed', 'plaintiff_id', 'plaintiff_names']],
                left_on='case_id',
                right_on='id',
                how='left'
            )

            # Add plaintiff name
            merged_df = pd.merge(
                merged_df,
                cache_manager.cached_plaintiff_df[['id', 'plaintiff_name']],
                left_on='plaintiff_id',
                right_on='id',
                how='left',
                suffixes=('', '_plaintiff')
            )

            # Rename columns for clarity
            merged_df = merged_df.rename(columns={
                'plaintiff_name': 'current_plaintiff_name'
            })

            # Convert timestamp columns for JSON serialization
            for col in ['date_filed']: # Add other relevant date/timestamp columns if needed
                if col in merged_df.columns and pd.api.types.is_datetime64_any_dtype(merged_df[col]):
                    merged_df[col] = merged_df[col].apply(lambda x: x.isoformat() if pd.notna(x) else None)

            # Replace NaN/NaT with None for JSON
            merged_df = merged_df.replace({pd.NA: None, pd.NaT: None, np.nan: None})

            # Convert to list of dictionaries for JSON response
            reviews = merged_df.to_dict(orient='records')

            return jsonify({
                'success': True,
                'reviews': reviews
            })

        except Exception as e:
            log_message(f"Error getting plaintiff reviews: {e}", level="ERROR")
            # Optionally log traceback
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/plaintiff/cases/<int:case_id>', methods=['GET'])
    def get_cases_for_plaintiff(case_id):
        """API endpoint to get all cases for a specific plaintiff based on a case_id"""
        log_message(f"Request received for cases of plaintiff from case_id {case_id}", level="DEBUG")

        try:
            # Ensure cache is loaded
            if not cache_manager.refresh_cached_data(force=False):
                log_message("Error: Cache could not be loaded in get_cases_for_plaintiff", level="ERROR")
                return jsonify({"error": "Data cache not available"}), 500

            # Check if necessary cached data exists
            if cache_manager.cached_cases_df is None or cache_manager.cached_plaintiff_reviews is None:
                log_message("Error: Required cached dataframes are None", level="ERROR")
                return jsonify({"error": "Required data not found in cache"}), 500

            # Find the review for this case_id to get the plaintiff_id
            review_row = cache_manager.cached_plaintiff_reviews[
                cache_manager.cached_plaintiff_reviews['case_id'] == case_id
            ]

            if review_row.empty:
                return jsonify({'success': False, 'error': f'No review found for case_id {case_id}'}), 404

            # Get the plaintiff_id from the case
            case_row = cache_manager.cached_cases_df[cache_manager.cached_cases_df['id'] == case_id]
            if case_row.empty:
                return jsonify({'success': False, 'error': f'Case {case_id} not found'}), 404

            plaintiff_id = case_row['plaintiff_id'].iloc[0]

            # Get all cases for this plaintiff
            plaintiff_cases = cache_manager.cached_cases_df[
                cache_manager.cached_cases_df['plaintiff_id'] == plaintiff_id
            ].copy()

            # Convert timestamp columns for JSON serialization
            for col in ['date_filed']:
                if col in plaintiff_cases.columns and pd.api.types.is_datetime64_any_dtype(plaintiff_cases[col]):
                    plaintiff_cases[col] = plaintiff_cases[col].apply(lambda x: x.isoformat() if pd.notna(x) else None)

            # Replace NaN with None for JSON
            plaintiff_cases = plaintiff_cases.replace({pd.NA: None, pd.NaT: None, np.nan: None})

            # Convert to list of dictionaries
            cases = plaintiff_cases[['id', 'docket', 'date_filed', 'plaintiff_names']].to_dict(orient='records')

            return jsonify({
                'success': True,
                'cases': cases,
                'proposed_name': review_row['proposed_name'].iloc[0]
            })

        except Exception as e:
            log_message(f"Error getting cases for plaintiff: {e}", level="ERROR")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/plaintiff/reviews/submit', methods=['POST'])
    def submit_plaintiff_reviews():
        """API endpoint to process plaintiff review decisions"""
        data = request.get_json()

        if not data or 'decisions' not in data:
            return jsonify({'success': False, 'error': 'Missing decisions data'}), 400

        decisions = data['decisions']
        log_message(f"Received {len(decisions)} plaintiff review decisions.", level="INFO")

        try:
            # Force refresh before starting, to work with latest data
            if not cache_manager.refresh_cached_data(force=True):
                log_message("Error: Cache refresh failed before submitting reviews.", level="ERROR")
                return jsonify({"error": "Data cache not available"}), 500

            # Check if necessary cached data exists using cache_manager prefix
            if cache_manager.cached_plaintiff_reviews is None or cache_manager.cached_cases_df is None or cache_manager.cached_plaintiff_df is None:
                log_message("Error: Required cached dataframes are None in submit_plaintiff_reviews", level="ERROR")
                return jsonify({"error": "Required data not found in cache"}), 500

            # Directly modify the main cached dataframes
            reviews_to_update_df = pd.DataFrame()
            reviews_to_delete_ids = []
            update_cases_df = pd.DataFrame() # Store cases that need plaintiff_id update
            cases_to_reprocess_df = pd.DataFrame() # Store case rows that need reprocessing

            # Process each decision
            for decision in decisions:
                try:
                    case_id = int(decision['case_id']) # Ensure case_id is int
                    action = decision['action']
                except (ValueError, KeyError):
                    log_message(f"Skipping invalid decision format: {decision}", level="WARNING")
                    continue

                # Find the review row index in the main cache
                review_indices = cache_manager.cached_plaintiff_reviews.index[cache_manager.cached_plaintiff_reviews['case_id'] == case_id].tolist()

                if not review_indices:
                    log_message(f"Review for case_id {case_id} not found in cache, skipping.", level="WARNING")
                    continue

                idx = review_indices[0] # Assume unique case_id in reviews

                if action == 'reject':
                    proposed_name = cache_manager.cached_plaintiff_reviews.loc[idx, 'proposed_name']
                    current_rejected_json = cache_manager.cached_plaintiff_reviews.loc[idx, 'rejected_names']

                    try:
                        current_rejected_list = json.loads(current_rejected_json) if pd.notna(current_rejected_json) and isinstance(current_rejected_json, str) else []
                    except json.JSONDecodeError:
                        current_rejected_list = []

                    # Add the proposed name to rejected list if not already present
                    if proposed_name and proposed_name not in current_rejected_list:
                        current_rejected_list.append(proposed_name)

                    # Update the main review dataframe directly
                    cache_manager.cached_plaintiff_reviews.loc[idx, 'rejected_names'] = json.dumps(current_rejected_list)
                    cache_manager.cached_plaintiff_reviews.loc[idx, 'proposed_name'] = None # Clear proposed name
                    cache_manager.cached_plaintiff_reviews.loc[idx, 'method_info'] = 'Rejected by user' # Update method info
                    cache_manager.cached_plaintiff_reviews.loc[idx, 'status'] = 'rejected' # Assuming 'status' column exists

                    # Add to df for batch DB update
                    reviews_to_update_df = pd.concat([reviews_to_update_df, cache_manager.cached_plaintiff_reviews.loc[[idx]]], ignore_index=False)

                elif action == 'approve':
                    # Get selected case IDs for reprocessing (new feature)
                    selected_case_ids = decision.get('selected_case_ids', [])

                    # Find the case row index in the main cache
                    case_indices = cache_manager.cached_cases_df.index[cache_manager.cached_cases_df['id'] == case_id].tolist()
                    if not case_indices:
                        log_message(f"Case ID {case_id} not found in cache for approval.", level="WARNING")
                        continue
                    case_idx = case_indices[0]

                    # Get the approved name from the review row
                    approved_name = cache_manager.cached_plaintiff_reviews.loc[idx, 'proposed_name']
                    if not approved_name:
                        log_message(f"No proposed name found for case ID {case_id} to approve.", level="WARNING")
                        continue

                    # Create a temporary DataFrame slice for add_plaintiff_id
                    case_row_slice = cache_manager.cached_cases_df.loc[[case_idx]]
                    case_row_slice['plaintiff_id'] = None # Clear plaintiff ID for add_plaintiff_id logic
                    case_row_slice['approved_plaintiff_name'] = approved_name # Use temp column

                    # Call add_plaintiff_id - it should modify cache_manager.cached_plaintiff_df and return the updated case slice
                    add_plaintiff_id(case_row_slice, cache_manager.cached_plaintiff_df) # Modifies cache_manager.cached_plaintiff_df and case_row_slice

                    if not case_row_slice.empty:
                        new_plaintiff_id = case_row_slice['plaintiff_id'].iloc[0]
                        # Store the change for batch case update
                        update_cases_df = pd.concat([update_cases_df, case_row_slice[['id', 'plaintiff_id', 'docket', 'court', 'date_filed', 'title']]], ignore_index=True)
                        # Update the main cases cache directly
                        cache_manager.cached_cases_df.loc[case_idx, 'plaintiff_id'] = new_plaintiff_id

                        # Only reprocess selected cases instead of all cases for this plaintiff
                        if selected_case_ids:
                            # Get only the selected cases for reprocessing
                            selected_cases_mask = cache_manager.cached_cases_df['id'].isin(selected_case_ids)
                            selected_cases_for_reprocess = cache_manager.cached_cases_df[selected_cases_mask]

                            # Update plaintiff_id for all selected cases
                            cache_manager.cached_cases_df.loc[selected_cases_mask, 'plaintiff_id'] = new_plaintiff_id

                            # Add selected cases to reprocess list
                            cases_to_reprocess_df = pd.concat([cases_to_reprocess_df, selected_cases_for_reprocess], ignore_index=False)

                            # Also update the database for all selected cases
                            selected_cases_update = cache_manager.cached_cases_df[selected_cases_mask][['id', 'plaintiff_id', 'docket', 'court', 'date_filed', 'title']]
                            update_cases_df = pd.concat([update_cases_df, selected_cases_update], ignore_index=True)

                            log_message(f"Will reprocess {len(selected_case_ids)} selected cases for approved plaintiff name.", level="INFO")
                        else:
                            # Fallback to original behavior if no cases selected
                            cases_to_reprocess_df = pd.concat([cases_to_reprocess_df, cache_manager.cached_cases_df.loc[[case_idx]]], ignore_index=False)
                            log_message(f"No cases selected for reprocessing, defaulting to original case only.", level="WARNING")

                    # Mark this review for deletion
                    reviews_to_delete_ids.append(case_id)

            # --- Batch Updates After Processing Decisions ---

            # Update rejected/updated reviews in DB
            if not reviews_to_update_df.empty:
                insert_and_update_df_to_GZ_batch(reviews_to_update_df, "tbi_case_plaintiff_name_review", "case_id")
                log_message(f"Updated {len(reviews_to_update_df)} rejected/updated reviews in DB.", level="INFO")

            # Update approved cases (plaintiff_id) in DB
            if not update_cases_df.empty:
                insert_and_update_df_to_GZ_batch(update_cases_df, "tb_case", "id")
                log_message(f"Updated plaintiff_id for {len(update_cases_df)} approved cases in DB.", level="INFO")

                # Reprocess the approved cases using new approach
                if not cases_to_reprocess_df.empty:
                    log_message(f"Reprocessing {len(cases_to_reprocess_df)} approved cases...", level="INFO")
                    # Use the new reprocessing approach with default options
                    success = reprocess_cases(cases_to_reprocess=cases_to_reprocess_df, trace_name="Plaintiff Review Reprocess", full_cases_df=cache_manager.cached_cases_df, plaintiff_df=cache_manager.cached_plaintiff_df)

            # Delete approved reviews from cache and DB
            if reviews_to_delete_ids:
                # Remove from cache
                cache_manager.cached_plaintiff_reviews.drop(
                    cache_manager.cached_plaintiff_reviews[cache_manager.cached_plaintiff_reviews['case_id'].isin(reviews_to_delete_ids)].index,
                    inplace=True
                )
                # Remove from DB
                query = f"DELETE FROM tbi_case_plaintiff_name_review WHERE case_id IN ({', '.join(map(str, reviews_to_delete_ids))})"
                try:
                    with get_gz_connection() as conn:
                        with conn.cursor() as cursor:
                            cursor.execute(query)
                            conn.commit()
                    log_message(f"Deleted {len(reviews_to_delete_ids)} approved reviews from DB.", level="INFO")
                except Exception as db_e:
                    log_message(f"Error deleting approved reviews from DB: {db_e}", level="ERROR")
                    # Consider potential inconsistency here

            # Save updated caches to feather files for persistence
            try:
                save_df_to_feather(cache_manager.cached_cases_df, "tb_case")
                save_df_to_feather(cache_manager.cached_plaintiff_df, "tb_plaintiff")
                save_df_to_feather(cache_manager.cached_plaintiff_reviews, "tbi_case_plaintiff_name_review")
                log_message("Saved updated caches to feather files.", level="INFO")
            except Exception as feather_e:
                 log_message(f"Error saving caches to feather files: {feather_e}", level="ERROR")
                 # This might lead to stale cache on next restart if DB was updated

            processed_count = len(reviews_to_update_df) + len(reviews_to_delete_ids)

            return jsonify({
                'success': True,
                'processed_count': processed_count
            })

        except Exception as e:
            log_message(f"Error processing plaintiff reviews: {e}", level="ERROR")
            # Optionally log traceback
            import traceback
            traceback.print_exc()
            # Attempt cache refresh on error
            try: cache_manager.refresh_cached_data(force=True)
            except: pass
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/plaintiff/run-improvement', methods=['POST']) # Added route decorator
    def run_improve_9_and_len2():
        """API endpoint to run the improve_plaintiff_9_and_2_letter_names function"""

        try:
            # Ensure cache is loaded
            if not cache_manager.refresh_cached_data(force=True): # Force refresh before running
                 log_message("Error: Cache refresh failed before running improvement.", level="ERROR")
                 return jsonify({"error": "Data cache not available"}), 500

            if cache_manager.cached_cases_df is None or cache_manager.cached_plaintiff_df is None or cache_manager.cached_plaintiff_reviews is None:
                 log_message("Error: Required caches are None before running improvement.", level="ERROR")
                 return jsonify({"error": "Required data caches not available"}), 500

            # This function likely modifies tbi_case_plaintiff_name_review directly
            # Pass the main cached dataframes - assume it modifies them directly or handles updates
            improve_plaintiff_9_and_2_letter_names(
                cache_manager.cached_cases_df,
                cache_manager.cached_plaintiff_df,
                cache_manager.cached_plaintiff_reviews
                )

            # Force refresh the cache after the operation to reflect changes
            cache_manager.refresh_cached_data(force=True)
            log_message("Cache refreshed after improve_plaintiff_9_and_2_letter_names.", level="INFO")

            # Count reviews in the updated cache
            review_count = len(cache_manager.cached_plaintiff_reviews) if cache_manager.cached_plaintiff_reviews is not None else 0

            return jsonify({
                'success': True,
                'message': f"Improvement process completed. Review table potentially updated. Current review count: {review_count}"
            })

        except Exception as e:
            log_message(f"Error running improve_plaintiff_9_and_2_letter_names: {e}", level="ERROR")
            # Optionally log traceback
            import traceback
            traceback.print_exc()
            # Attempt refresh on error
            try: cache_manager.refresh_cached_data(force=True)
            except: pass
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/plaintiff/without-cases', methods=['GET'])
    def get_plaintiffs_without_cases():
        """API endpoint to get the list of plaintiffs without cases"""
        log_message("Request received for /api/plaintiff/without-cases", level="DEBUG")
        try:
            # Ensure cache is loaded
            if not cache_manager.refresh_cached_data(force=False):
                 log_message("Error: Cache could not be loaded in get_plaintiffs_without_cases", level="ERROR")
                 return jsonify({"error": "Data cache not available"}), 500

            # Access cache using cache_manager prefix
            if cache_manager.cached_cases_df is None or cache_manager.cached_plaintiff_df is None:
                 log_message("Error: Cases or Plaintiff cache is None", level="ERROR")
                 return jsonify({"error": "Required data not available"}), 500

            # Get the list using the cached data (don't delete here)
            # Pass main cache dataframes
            plaintiffs = delete_plaintiff_without_cases(
                cache_manager.cached_cases_df,
                cache_manager.cached_plaintiff_df,
                perform_delete=False
            )

            return jsonify({
                'success': True,
                'plaintiffs': plaintiffs # plaintiffs is expected to be a list of dicts [{id: x, name: y}, ...]
            })

        except Exception as e:
            log_message(f"Error getting plaintiffs without cases: {e}", level="ERROR")
             # Optionally log traceback
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/plaintiff/delete-without-cases', methods=['POST'])
    def delete_plaintiffs_without_cases_api(): # Renamed function
        """API endpoint to delete selected plaintiffs without cases"""
        data = request.get_json()

        if not data or 'plaintiff_ids' not in data:
            log_message("Missing plaintiff_ids in request to delete plaintiffs without cases", level="WARNING")
            return jsonify({'success': False, 'error': 'Missing plaintiff_ids data'}), 400

        plaintiff_ids_to_delete = data['plaintiff_ids']
        if not isinstance(plaintiff_ids_to_delete, list):
            log_message("plaintiff_ids is not a list", level="WARNING")
            return jsonify({'success': False, 'error': 'plaintiff_ids must be a list'}), 400

        log_message(f"Request received to delete plaintiffs without cases: {plaintiff_ids_to_delete}", level="INFO")

        deleted_count = 0
        try:
            # Ensure cache is loaded (force refresh needed before delete)
            if not cache_manager.refresh_cached_data(force=True):
                 log_message("Error: Cache could not be refreshed before deleting plaintiffs", level="ERROR")
                 return jsonify({"error": "Data cache not available"}), 500

            # Access cache using cache_manager prefix
            if cache_manager.cached_plaintiff_df is None:
                 log_message("Error: Plaintiff cache is None, cannot delete", level="ERROR")
                 return jsonify({"error": "Plaintiff data not available"}), 500

            # Convert IDs to int for safe comparison and deletion
            valid_ids_to_delete = []
            for plaintiff_id in plaintiff_ids_to_delete:
                try:
                    valid_ids_to_delete.append(int(plaintiff_id))
                except (ValueError, TypeError):
                     log_message(f"Invalid plaintiff ID format skipped: {plaintiff_id}", level="WARNING")

            if not valid_ids_to_delete:
                 return jsonify({'success': True, 'deleted_count': 0, 'message': 'No valid plaintiff IDs provided for deletion.'})


            # Use DB connection to delete specified IDs
            placeholders = ', '.join(['%s'] * len(valid_ids_to_delete))
            sql = f"DELETE FROM tb_plaintiff WHERE id IN ({placeholders})"

            with get_gz_connection() as connection:
                with connection.cursor() as cursor:
                    try:
                        cursor.execute(sql, tuple(valid_ids_to_delete))
                        deleted_count = cursor.rowcount # Get the actual number of deleted rows
                        connection.commit()
                        log_message(f"Deleted {deleted_count} plaintiffs from database: {valid_ids_to_delete}", level="INFO")
                    except Exception as db_del_e:
                         log_message(f"Error deleting plaintiff IDs {valid_ids_to_delete} from DB: {db_del_e}", level="ERROR")
                         connection.rollback() # Rollback on error
                         # Re-raise or return error? Returning error for now.
                         raise db_del_e # Re-raise to be caught by outer try-except


            # Force refresh the cache AFTER deletions are committed
            cache_manager.refresh_cached_data(force=True)
            log_message("Cache refreshed after deleting plaintiffs.", level="INFO")

            return jsonify({
                'success': True,
                'deleted_count': deleted_count
            })

        except Exception as e:
            log_message(f"Error deleting plaintiffs without cases: {e}", level="ERROR")
            # Optionally log traceback
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    # Add other routes previously in this file if they should remain
    # Ensure they also use the cache manager appropriately

    return app # Return app for Flask registration
