I. Data Acquisition and Processing Scripts
A. Historical Batch Import Script
•	Purpose: Perform a one-time bulk import of historical trademark data (pre-2025) provided by USPTO.
•	Source: USPTO Bulk Data Distribution System (BDSS).
•	Requirements:
1.	Identify and list the URLs for all 82 historical ZIP files.
https://data.uspto.gov/ui/datasets/products/files/TRTYRAP/apc18840407-20241231-xx.zip where xx goes from 01 to 82

2.	Implement functionality to download each ZIP file reliably, handling potential network errors and retries. Save the 82 historical zip in ./pre2025
3.	For each downloaded ZIP file:
	Extract the contents (expected to be a single XML file per ZIP).
	Identify the extracted XML file path.
	Call the parse_daily_trademark_xml function (defined in Section II.B) with the content of the XML file. This function returns a list of dictionaries, each representing a trademark record.
	Handle potential errors during extraction or parsing for individual files (log errors and continue if possible).

B. Daily Catch-up Script
•	Purpose: Download and process historical daily trademark update files from a specified start date (Jan 1st, 2025) up to the current date.
•	Source: USPTO API for Daily Trademark Data (TRTDXFAP product). Requires API key handling.
•	Endpoint Pattern: https://api.uspto.gov/api/v1/datasets/products/files/TRTDXFAP/apc{YYMMDD}.zip (Requires constructing the date part). Note: YYYYMMDD will not work. We need YYDDMM for daily files.
•	Requirements:
1.	Accept a start date ('2025-01-01') and determine the end date (today's date).
2.	Iterate through each date from the start date to the end date.
3.	For each date:
	Format the date as YYMMDD.
	Construct the full API download URL.
	Attempt to download the ZIP file using the constructed URL. Handle potential API errors gracefully (e.g., 404 Not Found if no file exists for a specific day, rate limiting errors, network issues). Implement retries for transient errors.
Save the zip files in ./daily
	If a ZIP file is successfully downloaded:
	Extract the contents (expected to be a single XML file).
	Identify the extracted XML file path.

C. Ongoing Daily Processing Script
•	Purpose: Automatically download, parse, and load the latest daily trademark update file every day.
•	Scheduling: Must be run via a scheduler (e.g., cron, systemd timer, Airflow, AWS Lambda scheduled event, Azure Functions Timer Trigger, Google Cloud Scheduler).
•	Frequency: Daily. The script should run early enough to allow USPTO time to publish the previous day's file (e.g., run early morning EST).
•	Source: USPTO API for Daily Trademark Data (TRTDXFAP product), same endpoint pattern as the Catch-up Script.
•	Requirements:
1.	Determine the date for which to fetch the file (usually the previous calendar day).
2.	Format the date as YYYYMMDD.
3.	Construct the full API download URL.
4.	Attempt to download the ZIP file. Implement robust error handling for API/network issues, including retries. If the file is not available (e.g., 404), log this and exit gracefully (it might be a holiday or publishing delay).
5.	If the ZIP file is successfully downloaded:
	Extract the contents.
	Identify the extracted XML file path.
	Call the parse_daily_trademark_xml function.
	Process the returned list of dictionaries by loading them into the trademarks table using UPSERT logic based on ser_no. Update relevant fields and last_updated_* metadata.
6.	Implement comprehensive logging for the entire process.
7.	Implement error notification (e.g., email, Slack message) if the script fails to download, parse, or load the data after retries, to alert administrators.

II. XML Parsing Function Requirements. Function: parse_bulk_trademark_xml
•	Input: String containing the XML content of a USPTO Daily/Bulk file (containing one or more <case-file> elements).
•	Output: A list of dictionaries. Each dictionary represents one row for the trademarks table, corresponding to one parsed <case-file>. Return [] if no valid <case-file> elements are found.
•	Core Logic & Field Extraction:
o	Iterate through all <case-file> elements found in the input XML.
o	For each <case-file>, extract data according to the detailed field-by-field instructions in the "Revised Field Mapping Table" (Section 2 of the previous response), specifically the column "Daily Trademark XML Path/Instructions".
o	Ensure correct selection logic (e.g., highest entry number owner), data type conversions (including 'T'/'F' to Boolean, 'YYYYMMDD' to Date), JSON formatting for goods_services and case_file_statements_other, and graceful handling of missing elements within a <case-file>.
o	Set fields not present in Daily/Bulk format (e.g., associated_marks, mark_disclaimer_text, national_design_code) to None or [].
o	Populate the new daily-specific fields (mark_disclaimer_text_daily, case_file_statements_other, etc.) as per the table.
o	Set last_updated_source based on context (e.g., 'daily_api', 'historical_bulk').
o	Append the resulting dictionary for the <case-file> to the output list.
o	If parsing a specific <case-file> fails critically, log the error (including serial-number if possible) and continue to the next <case-file>.

For each trademak that has mark_feature_code equal to 2 3 or 5 we want to download the image of the mark. The way to download it is to use 
    api_client = TSDRApi()
    image_url = f"https://tsdr.uspto.gov/img/{serial_number}/large"
    image_data = await api_client.download_from_uspto(image_url)
    image_path= ./Images/XX/YY/serial_number.webp where XX is are the 2 last number of serial_number and YY are the 2 numbers before that.
    if image_data:
        image = Image.open(io.BytesIO(image_data))
        image.save(image_path, "WEBP")
        then set the image_source field in the database to USPTO_URL

    else:
            then set the image_source field in the database to "NotFound" 
            print(f"🔥 Error: No image found for {formatted_reg_no}")

III. Send the parse info to the database based on the postgress database structure.
Implement logic to load the aggregated data into the trademarks PostgreSQL table. Consider using bulk loading methods for efficiency. Handle potential database errors. Use an INSERT ... ON CONFLICT (ser_no) DO UPDATE ... strategy (UPSERT) to handle potential overlaps if the script is re-run or overlaps with other data sources, updating relevant fields and last_updated_* metadata.

One trademark can appear again (in a subsequent zip/xml file), in that case, you upsert the database. Hence you may not want to set null for any feild but rather not mention the empty field, so that we don't overwrite if something was already there


IV. File management: 
For all: after processing the XML file, delete the XML file but keep the zip file
Send Zip file to NAS as backup (we can use our NASConnection class):
with NASConnection() as nas:
    nas.send_files_to_nas(local_folder, remote_folder) => will zip and send an entire folder
    nas.self.transfer_file_with_scp(local_path=local_zip_path, remote_path=remote_zip_path, to_nas=True) => will send a single file


Overall strategy:
- process the files in order: pre2025, daily (jan 1st, etc...), ongoing. So the flow is 
pre2025 file 1 -> download zip -> extract -> processXML -> download images -> update the database -> send to NAS
pre2025 file 2 -> ..... 
....
daily file 1 -> download zip -> extract -> processXML -> download images -> update the database -> send to NAS
daily file 2 -> .....
etc....
Then everyday we do the ongoing for one day:
ongoing file 1 -> download zip -> extract -> processXML -> download images -> update the database -> send to NAS

- you use async as much as possible but wait for everything to finish before moving to the next zip file. 
A. we can start sending the zip file to the NAS almost imediatly after download (and only wait it at the end of the zip processing)
B. we can update database and send images to NAS in aparalel, because they do not depend on each other
C. More advanced: within 1 XML file, we can process multiple trademark in parrallell. Downloading the image is IO bound. How about parse_daily_trademark_xml? if IO bound, then also using async. Otherwise we might want to use multiprocessing to benefit from many cores.

- think carefully about reusability of code. It seems to me that daily_catchup and ongoing_daily_processing should be calling the same function that processes a single date.

- You create a test function that will get a daily for 2025-03-15 and process it and send to nas and to database for testing.
- Implement logging for the whole process (success, failure, errors).
- Note: In each zip, there is only 1 XML file
 


Appendix: This is detail instruction about how to get each field from the XML file
Database Field	Daily Trademark XML Path/Instructions
reg_no (TEXT)	/case-file/registration-number
ser_no (TEXT)	/case-file/serial-number
TRO (TEXT)	Null
applicant_name (TEXT)	Find case-file-owner with the highest entry-number. Extract party-name.
mark_text (TEXT)	/case-file/case-file-header/mark-identification
int_cls (BIGINT[])	Collect unique /case-file/classifications/classification/international-code values. Convert to BIGINT.
filing_date (DATE)	/case-file/case-file-header/filing-date (Parse 'YYYYMMDD')
nb_suits (BIGINT)	Count occurrences of /case-file/case-file-event-statements/case-file-event-statement where code text is 'NOSUI'.
country_codes (TEXT[])	Find case-file-owner with the highest entry-number. Collect unique nationality/country values from within that owner element.
associated_marks (TEXT[])	Null
mark_current_status_code (INTEGER)	/case-file/case-file-header/status-code (Convert to INTEGER)
mark_feature_code (INTEGER)	/case-file/case-file-header/mark-drawing-code (Convert to INTEGER)
mark_standard_character_indicator (BOOLEAN)	/case-file/case-file-header/standard-characters-claimed-in (Parse 'T'/'F')
mark_disclaimer_text (TEXT[])	Null
mark_image_colour_claimed_text (TEXT)	Null
mark_image_colour_part_claimed_text (TEXT)	Null
national_design_code (TEXT[])	Null
goods_services (JSONB)	Parse /case-file/classifications/classification elements. Structure as: [{NiceClass: international-code, StatusCode: status-code, FirstDateUsed: first-use-anywhere-date, FirstUsedInCommerceDate: first-use-in-commerce-date, subclass: null}, ...] (Parse dates from 'YYYYMMDD')
mark_current_status_external_description_text (TEXT)	Null
mark_disclaimer_text_daily (TEXT[])	Collect <text> from /case-file/case-file-statements/case-file-statement where type-code is 'D0' or 'D1'.
mark_image_colour_statement_daily (TEXT[])	Collect <text> from /case-file/case-file-statements/case-file-statement where type-code is 'CC' or 'CD'.
mark_translation_statement_daily (TEXT)	Get <text> from /case-file/case-file-statements/case-file-statement where type-code is 'TR'. (Take first if multiple).
name_portrait_statement_daily (TEXT)	Get <text> from /case-file/case-file-statements/case-file-statement where type-code is 'N0'. (Take first if multiple).
mark_description_statement_daily (TEXT)	Get <text> from /case-file/case-file-statements/case-file-statement where type-code is 'DM'. (Take first if multiple).
certification_mark_statement_daily (TEXT)	Get <text> from /case-file/case-file-statements/case-file-statement where type-code is 'CS'. (Take first if multiple).
lining_stippling_statement_daily (TEXT)	Get <text> from /case-file/case-file-statements/case-file-statement where type-code is 'LS'. (Take first if multiple).
section_2f_statement_daily (TEXT)	Get <text> from /case-file/case-file-statements/case-file-statement where type-code is 'TF'. (Take first if multiple).
goods_services_text_daily (TEXT)	Get <text> from /case-file/case-file-statements/case-file-statement where type-code starts with 'GS'. (Combine if multiple, though usually one).
case_file_statements_other (JSONB)	Collect { "type_code": type-code, "text": text } for all /case-file/case-file-statements/case-file-statement where type-code is NOT one of ('D0', 'D1', 'CC', 'CD', 'TR', 'N0', 'DM', 'CS', 'LS', 'TF', or starts with 'GS').
latest_event_description_daily (TEXT)	Find /case-file/case-file-event-statements/case-file-event-statement with the latest date (and highest number for ties). Extract description-text.
    
Abbreviations:	
GSC: ns2:GoodsServicesClassification	
CD: ns2:ClassDescription	
NSB: ns2:NationalStatusBag	
NS: ns2:NationalStatus	
NFB: ns2:NationalFilingBasis	
