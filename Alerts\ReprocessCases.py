# Alerts/ReprocessCases.py
"""
Centralized case reprocessing functionality.
This module provides a unified interface for reprocessing cases across different parts of the application.
"""

import time
import pandas as pd
from typing import Dict, Any, Optional, List, Union
from logdata import log_message
from DatabaseManagement.ImportExport import get_table_from_GZ


def reprocess_cases(
    cases_to_reprocess: Union[pd.DataFrame, List[int], int],
    processing_options: Optional[Dict[str, Any]] = None,
    trace_name: str = "Case Reprocess",
    full_cases_df: Optional[pd.DataFrame] = None,
    plaintiff_df: Optional[pd.DataFrame] = None
) -> bool:
    """
    Unified function for reprocessing cases using the new approach.

    Args:
        cases_to_reprocess: Can be:
            - DataFrame containing cases to reprocess
            - List of case IDs
            - Single case ID (int)
        processing_options: Dictionary of processing options. If None, uses default options.
        trace_name: Name for the trace (for logging/monitoring)
        full_cases_df: Full cases DataFrame. If None, will be loaded from database.
        plaintiff_df: Plaintiff DataFrame. If None, will be loaded from database.

    Returns:
        bool: True if all cases processed successfully, False otherwise
    """

    # Import required modules
    from Alerts.LexisNexis import Login
    from Alerts.Arrive_On_Case_Page import arrive_on_case_page
    from Alerts.CaseProcessor import setup_trace, process_case

    # Default processing options
    if processing_options is None:
        processing_options = {
            'update_steps': True,
            'process_pictures': True,
            'upload_files_nas': True,
            'upload_files_cos': True,
            'run_plaintiff_overview': True,
            'run_summary_translation': True,
            'run_step_translation': True,
            'save_to_db': True,
            'processing_mode': 'full_reprocess',
            'refresh_days_threshold': 15
        }

    # Convert input to DataFrame of cases to reprocess
    if isinstance(cases_to_reprocess, int):
        # Single case ID
        case_ids = [cases_to_reprocess]
    elif isinstance(cases_to_reprocess, list):
        # List of case IDs
        case_ids = cases_to_reprocess
    elif isinstance(cases_to_reprocess, pd.DataFrame):
        # DataFrame - extract case IDs
        if 'id' in cases_to_reprocess.columns:
            case_ids = cases_to_reprocess['id'].tolist()
        else:
            log_message("DataFrame must contain 'id' column", level="ERROR")
            return False
    else:
        log_message(f"Invalid cases_to_reprocess type: {type(cases_to_reprocess)}", level="ERROR")
        return False

    if not case_ids:
        log_message("No cases to reprocess", level="INFO")
        return True

    # Load DataFrames if not provided
    if full_cases_df is None:
        log_message("Loading cases DataFrame from database...", level="INFO")
        full_cases_df = get_table_from_GZ("tb_case", force_refresh=False)
        if full_cases_df is None or full_cases_df.empty:
            log_message("Failed to load cases DataFrame", level="ERROR")
            return False

    if plaintiff_df is None:
        log_message("Loading plaintiff DataFrame from database...", level="INFO")
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)
        if plaintiff_df is None or plaintiff_df.empty:
            log_message("Failed to load plaintiff DataFrame", level="ERROR")
            return False

    # Filter cases_df to only include cases we want to reprocess
    cases_to_reprocess_df = full_cases_df[full_cases_df['id'].isin(case_ids)]

    if cases_to_reprocess_df.empty:
        log_message(f"No matching cases found in database for IDs: {case_ids}", level="WARNING")
        return False

    log_message(f"Starting reprocessing of {len(cases_to_reprocess_df)} cases with {trace_name}", level="INFO")

    success_count = 0
    total_count = len(cases_to_reprocess_df)

    try:
        # Get logged-in browser
        log_message("Getting logged-in browser...", level="INFO")
        driver = Login.get_logged_in_browser()

        if not driver:
            raise Exception("Failed to get logged-in browser")

        # Process each case
        for idx, case_row in cases_to_reprocess_df.iterrows():
            case_id = case_row['id']

            try:
                log_message(f"Reprocessing case {case_id} ({case_row.get('docket', 'Unknown docket')})", level="INFO")

                # Setup trace for this case
                case_processor_trace = setup_trace(trace_name, case_identifier={'case_id': case_id}, processing_options=processing_options)

                # Arrive on case page
                arrival_success, arrival_df_slice, case_idx = arrive_on_case_page(driver, full_cases_df, case_processor_trace, case_identifier={'case_id': case_id})

                if not arrival_success:
                    log_message(f"Failed to arrive on case page for case {case_id}", level="WARNING")
                    continue

                # Process the case
                success, updated_case_df_row = process_case(driver, processing_options, full_cases_df, case_idx, plaintiff_df, case_processor_trace)

                if success:
                    log_message(f"Successfully reprocessed case {case_id}", level="INFO")
                    success_count += 1
                else:
                    log_message(f"Reprocessing completed with warnings for case {case_id}", level="WARNING")

            except Exception as case_e:
                log_message(f"Error reprocessing case {case_id}: {str(case_e)}", level="ERROR")
                continue

        # Close the browser
        driver.quit()
        log_message(f"Completed reprocessing: {success_count}/{total_count} cases successful", level="INFO")

        return success_count == total_count

    except Exception as e:
        log_message(f"Critical error during case reprocessing: {str(e)}", level="ERROR")
        import traceback
        log_message(traceback.format_exc(), level="ERROR")
        return False



# Example Usage (Illustrative - requires actual instances)
if __name__ == '__main__':
    start_time = time.time()
    from Alerts.LexisNexis import Login
    from Alerts.Arrive_On_Case_Page import arrive_on_case_page
    # This block is for testing/illustration purposes only
    # Replace with actual setup in your application context
    print("CaseProcessor module loaded. Define actual instances and call process_case.")
    # Example:
    cases_df = get_table_from_GZ("tb_case", force_refresh=True) # Added force_refresh=False
    plaintiffs_df = get_table_from_GZ("tb_plaintiff", force_refresh=True) # Added force_refresh=False

    case_id_to_process = 3566  # Patent, Exhibit
    case_id_to_process = 13167  # Patent, Chinese website, or 12522
    case_id_to_process = 12516  # Patent, By Name
    case_id_to_process = 13536  # Trademark, Exhibit
    case_id_to_process = 12509   # cv-02336, Trademark, By Name
    case_id_to_process = 12956  # cv-20585, Trademark, By Name
    case_id_to_process = 9305  # 1:24-cv-11152, Coyright, Exhibit, PA
    case_id_to_process = 13609  # 1:25-cv-05268, Coyright, Exhibit, VA
    # case_id_to_process = 13167  # Coyright, Chinese website
    # case_id_to_process =   # Coyright, By Name
    case_id_to_process = 13644 #   # 13641, 13642, 13644
    case_id_to_process = 13708  # 13708 (download model problem+ Cn website driver)  1503, 49 (1:24cv8481)

    my_df = cases_df[pd.isna(cases_df['plaintiff_id'])] # Cases without plaintiff_id
    cases_df_sorted = cases_df.sort_values(by=['plaintiff_id', 'date_filed'], ascending=[True, False])
    cases_df_most_recent = cases_df_sorted.drop_duplicates(subset=['plaintiff_id'], keep='first')
    my_df = cases_df_most_recent[(cases_df_most_recent["images"] == {'trademarks': {}, 'patents': {}, 'copyrights': {}}) & (cases_df_most_recent["nos_description"].notna())]
    my_df_with_ai_summary = my_df[my_df["aisummary"].fillna("") != ""]
    my_df_without_ai_summary = my_df[my_df["aisummary"].fillna("") == ""]
    #=> we have 3 priority groups: 
    # (1) new cases without plaintiff ID, 
    # (2) existing cases (1 per plaintiff) without AI summary, 
    # (3) existing cases (1 per plaintiff) with AI summary 
    
    processing_options = {
        'update_steps': True, # Forces update of case steps from source
        'process_pictures': True, # Process images from PDFs, always true for now!
        'upload_files_nas': True, # Upload files to NAS
        'upload_files_cos': True, # Upload files to COS
        'run_plaintiff_overview': True, # Run AI plaintiff overview
        'run_summary_translation': True, # Run AI summary translation
        'run_step_translation': True, # Run AI step translation
        'save_to_db': True, # Save results to DB
        'processing_mode': 'full_reprocess', # Processing mode: 'full_reprocess' (default) or 'resume'
        'refresh_days_threshold': 15 # Refresh threshold in days
    }
    
    reprocess_cases(cases_to_reprocess=my_df, processing_options=processing_options, trace_name= "Manual Case Processor", full_cases_df=cases_df, plaintiff_df=plaintiffs_df)
    
    # reprocess_cases(cases_to_reprocess=case_id_to_process, processing_options=processing_options, trace_name= "Manual Case Processor", full_cases_df=cases_df, plaintiff_df=plaintiffs_df)
