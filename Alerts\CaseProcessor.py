# Alerts/CaseProcessor.py
import time, os, getpass, asyncio, multiprocessing, traceback, shutil, json
from concurrent.futures import ThreadPoolExecutor, as_completed
start_time0 = time.time()
from datetime import datetime, date
from typing import Dict, Any, Optional, Tuple
import pandas as pd
if multiprocessing.current_process().name == 'MainProcess':
    print(f"Case processor: datetime, date, Dict, pd, os, re {time.time()-start_time0:.2f} seconds")

start_time = time.time()
from selenium.webdriver.common.by import By # Added for finding elements
if multiprocessing.current_process().name == 'MainProcess':
    print(f"Case processor: Selenium + Traceback {time.time()-start_time:.2f} seconds")

# Imports for LexisNexis interaction
start_time = time.time()
from Alerts.LexisNexis import Search_Cases, Get_Case_Details, Get_Case_Steps, Download_File
if multiprocessing.current_process().name == 'MainProcess':
    print(f"Case processor: Import LexisNexis {time.time()-start_time:.2f} seconds")  # 7 sec

from Alerts.LexisNexis.Download_File import get_step_details

# Imports for PicturesProcessing interaction
start_time = time.time()
from Alerts.PicturesProcessing import ProcessPictures
from Alerts.PicturesProcessing import Copyrights_UCO
from Alerts.PicturesProcessing import Copyrights_Main_PDF
from Alerts.PicturesProcessing import Trademarks_RegNo
if multiprocessing.current_process().name == 'MainProcess':
    print(f"Case processor: Import ProcessPictures {time.time()-start_time:.2f} seconds")  # 27 sec!

from DatabaseManagement.ImportExport import get_table_from_GZ, get_subscibed_cases, insert_and_update_df_to_GZ_batch, insert_and_update_df_to_GZ_id, update_feather_file # Added insert_and_update_df_to_GZ_id
from FileManagement.Tencent_COS import send_pictures_to_cos # Added for specific COS upload
from Alerts.PicturesProcessing import IPAnalysisLLM # Added for IP analysis
from Common.Constants import local_case_folder, nas_case_folder, sanitize_name # Added constants
from logdata import log_message
from FileManagement.NAS import NASConnection
from Alerts.IPTrackingManager import IPTrackingManager # Added IP Tracking Manager

# --- AI Task Imports ---
from AI.Translation import get_ai_summary_translations, translate_steps_for_a_case # Use specific function for single case
from Alerts.Plaintiff import add_plaintiff_id # Added for new case plaintiff handling
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch # Removed get_steps_for_case
from langfuse.decorators import observe, langfuse_context # Added langfuse
from Common.LangfuseClient import langfuse_client


if multiprocessing.current_process().name == 'MainProcess':
    print(f"Total Case processor import time:{time.time()-start_time0:.2f} seconds")
# ❌⚠️📥📤☁️ 🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


def setup_trace(name: str, case_identifier: Dict[str, Any], processing_options: Dict[str, Any]):
    case_procesor_trace = langfuse_client.trace(name=name, user_id=getpass.getuser(), input={"CaseIdentifier": case_identifier, "OptionsSummary": processing_options})
    return case_procesor_trace


def process_case(driver, options: Dict[str, Any], cases_df: pd.DataFrame, idx: Any, plaintiff_df: pd.DataFrame, case_procesor_trace: Any) -> Tuple[bool, pd.DataFrame]:
    """
    Processes a single case from the point of arriving on the case page.
    This is Part 2 of the original process_case function.

    Args:
        driver: Active Selenium WebDriver instance.
        options: Dictionary of processing flags and configurations.
        cases_df: DataFrame containing case data
        idx: Index of the case in cases_df.
        plaintiff_df: DataFrame containing plaintiff data.
        case_procesor_trace: Langfuse trace object.

    Returns:
        A tuple: (bool success_status, pd.DataFrame modified_case_row_df).
                 The DataFrame contains the single row for the processed case with updates.
                 Returns empty DataFrame on failure.
    """
    local_case_dir = None # Initialize for span logging in case of early exit
    copyright_template1_hash = None # Initialize for span logging
    nassyncaction_for_span = "NoTransfer/LocalCreated" # Default for span logging

    # Initialize statistics tracking
    pacer_refresh = 0  # Track if case was refreshed
    pacer_file = 0     # Track files retrieved from PACER
    total_file = 0     # Track total files downloaded

    docket_number = cases_df.loc[idx, 'docket']
    court = cases_df.loc[idx, 'court']

    trace_id = case_procesor_trace.id
    case_procesor_trace.update(session_id=f"{cases_df.at[idx, 'date_filed']} - {docket_number}")

    try:
        # --- Part 1 of this function: Folder setup, NAS sync, Copyright template ---
        nas_span = case_procesor_trace.span(
            name="nas_sync_and_folder_setup",
            input={"Docket": docket_number, "Court": cases_df.at[idx, 'court'], "NASCaseFolder": nas_case_folder, "LocalCaseFolder": local_case_folder}
        )
        try:
            # --- Check if local folder exists and compare with NAS ---
            local_case_dir = os.path.join(local_case_folder, sanitize_name(f"{cases_df.at[idx, 'date_filed'].strftime('%Y-%m-%d')} - {docket_number}"))
            local_case_images_dir = os.path.join(local_case_dir, "images")
            if os.path.exists(local_case_images_dir):
                shutil.rmtree(local_case_images_dir)
                log_message(f"🗑️  Removed existing images directory: {local_case_images_dir}", level='INFO')
            os.makedirs(local_case_images_dir, exist_ok=True)

            remote_nas_dir = f"{nas_case_folder}/{sanitize_name(f'{cases_df.at[idx, 'date_filed'].strftime('%Y-%m-%d')} - {docket_number}')}"

            with NASConnection() as nas:
                if not os.path.exists(local_case_dir) and nas.ssh_exists(remote_nas_dir):
                    log_message(f"📥 Local directory missing or empty. Transferring from NAS: {remote_nas_dir} to {local_case_dir}", level='INFO')
                    nas.ssh_nas_to_local(remote_folder=remote_nas_dir, local_folder=local_case_dir)
                    nassyncaction_for_span = "TransferredFromNAS"
                elif nas.ssh_exists(remote_nas_dir):
                    local_pdf_count = sum([len([f for f in files if f.lower().endswith('.pdf')]) for _, _, files in os.walk(local_case_dir)])
                    stdout = nas.ssh_get_folder_files_names(remote_nas_dir, "*.pdf")
                    remote_pdfs = stdout.strip().split('\n') if stdout.strip() else []
                    remote_pdf_count = len(remote_pdfs)

                    if remote_pdf_count > local_pdf_count:
                        log_message(f"📥 More PDFs on NAS ({remote_pdf_count}) than locally ({local_pdf_count}). Transferring from NAS.", level='INFO')
                        nas.ssh_nas_to_local(remote_folder=remote_nas_dir, local_folder=local_case_dir)
                        nassyncaction_for_span = "TransferredFromNAS"
                    else:
                        log_message(f"Local folder up to date with NAS. No transfer needed.", level='INFO')
                        nassyncaction_for_span = "NoTransfer/LocalCreated" # Or "LocalUpToDate"
                else:
                    log_message(f"📁 Local folder not found and NAS folder not found => creating the folder", level='INFO')
                    os.makedirs(local_case_dir, exist_ok=True)
                    nassyncaction_for_span = "NoTransfer/LocalCreated"


            # --- Get Copyright Template ---
            try:
                copyright_template1_hash, copyright_template1_size = ProcessPictures.get_template(os.path.join(os.getcwd(), 'data', 'ImageTemplates', 'Copyright1'))
                log_message("🖼️  Copyright template loaded.", level='DEBUG')
            except Exception as template_err:
                log_message(f"❌ Error getting copyright template: {template_err}", level='ERROR')
                cases_df.loc[idx, 'file_status'] = "Failed: Template Error"
                # Span and trace will be updated by the main exception handler of this function.
                return False, cases_df.loc[[idx]].copy() # Return relevant slice

            log_message("---------------------------------------\n")

        finally: # This finally is for the operations covered by span_identify_case within this function
            _copyright_loaded_str = 'Yes' if copyright_template1_hash else 'No'
            nas_span.end(output={"NASSyncAction": nassyncaction_for_span, "CopyrightTemplateLoaded": _copyright_loaded_str})

        # --- Part 2 of this function: Fetch details, process steps, AI, etc. ---

        # Fetch Case Details & Update Row/DataFrame
        log_message(f"ℹ️  Fetching case details (qualitative fields) for {docket_number} using driver.", level='INFO')
        try:


            pacer_refresh, title_has_changed = Get_Case_Details.get_case_details(cases_df, idx, driver, options, langfuse_parent_trace_id=trace_id)

            if cases_df.at[idx, "id"] is None: # If it was a new case
                log_message(f"ℹ️ Case {docket_number} was newly added. Saving to get ID...", level='INFO')
                # insert_and_update_df_to_GZ_id returns the whole df, we need to update cases_df and case_id
                updated_df_slice_with_id = insert_and_update_df_to_GZ_id(cases_df.loc[[idx]].copy(), "tb_case", "docket", "court")
                if not updated_df_slice_with_id.empty:
                    cases_df.loc[idx, 'id'] = updated_df_slice_with_id.iloc[0]['id']
                    log_message(f"ℹ️ Assigned new case ID: {cases_df.loc[idx, 'id']}", level='INFO')
                else:
                    log_message(f"❌ Failed to get ID for new case {docket_number}.", level='ERROR')
                    cases_df.loc[idx, 'file_status'] = "Failed: ID Assignment Error"
                    return False, cases_df.loc[[idx]].copy()

            if cases_df.loc[idx, 'plaintiff_id'] is None or pd.isna(cases_df.loc[idx, 'plaintiff_id']) or title_has_changed:
                log_message(f"Calling add_plaintiff_id for case {docket_number} (idx: {idx}). Reason: plaintiff_id={cases_df.loc[idx, 'plaintiff_id']}, title_has_changed={title_has_changed}", level='INFO')
                add_plaintiff_id(cases_df, plaintiff_df, [idx])
                cases_df['plaintiff_id'] = cases_df['plaintiff_id'].astype('Int64') # because if plaintiff_id has NaN, it was float by default

            log_message(f"✅ Case details fetched/refreshed for {docket_number} at index {idx}. Refreshed: {pacer_refresh}, Title Changed: {title_has_changed}", level='INFO')

            nos_description = cases_df.loc[idx].get("nos_description", "")

            # Get processing mode from options, default to 'full_reprocess'
            current_processing_mode = options.get('processing_mode', 'full_reprocess')
            loaded_ip_manager_successfully = False

            # Ensure images_status is initialized


            # Initialize images_status and ip_manager based on processing mode
            if current_processing_mode == 'resume':
                if isinstance(cases_df.at[idx, 'images'], str): # Already done when loaded from database
                    cases_df.at[idx, 'images'] = json.loads(cases_df.at[idx, 'images'])
                if isinstance(cases_df.at[idx, 'images_status'], str): # Not done when loaded from database. Should be consistent!
                    cases_df.at[idx, 'images_status'] = json.loads(cases_df.at[idx, 'images_status'])
                if not isinstance(cases_df.at[idx, 'images_status'], dict):
                    ProcessPictures.initialize_case_image_status(cases_df, idx)

                # Try to load saved state for resume mode
                try:
                    if 'ip_manager_state' in cases_df.at[idx, 'images_status']:
                        saved_state_json = cases_df.at[idx, 'images_status']['ip_manager_state']
                        if saved_state_json:
                            ip_manager = IPTrackingManager(nos_description_text=nos_description if pd.notna(nos_description) else "",
                                                          initial_state=saved_state_json)
                            loaded_ip_manager_successfully = True
                            log_message(f"📊 Loaded IPTrackingManager state for resume mode. Previously processed steps: {len(cases_df.at[idx, 'images_status']['steps_processed'])}", level='INFO')
                except Exception as e:
                    log_message(f"⚠️ Failed to load saved IPTrackingManager state: {e}. Will initialize fresh.", level='WARNING')

                # If loading failed, fall back to fresh initialization
                if not loaded_ip_manager_successfully:
                    ip_manager = IPTrackingManager(nos_description if pd.notna(nos_description) else "")
                    log_message(f"📊 No prior state found for resume, initialized fresh IPTrackingManager.", level='INFO')
            else:  # full_reprocess mode
                ProcessPictures.initialize_case_image_status(cases_df, idx)
                ip_manager = IPTrackingManager(nos_description if pd.notna(nos_description) else "")
                log_message(f"📊 Initialized fresh ImagesStatus and IPTrackingManager for full reprocess mode.", level='INFO')

            log_message(f"📊 IPTrackingManager relevant types: TM={ip_manager.is_goal_met('trademark')==False}, CR={ip_manager.is_goal_met('copyright')==False}, PT={ip_manager.is_goal_met('patent')==False}", level='INFO')

        except Exception as detail_err:
            log_message(f"❌ Error fetching case details for {docket_number}: {detail_err}\n{traceback.format_exc()}", level='ERROR')
            cases_df.loc[idx, 'file_status'] = "Failed: Detail Fetch Error"
            return False, cases_df.loc[[idx]].copy() # Return relevant slice

        # Update trace info
        if case_procesor_trace.get_trace_url():
            cases_df.at[idx, 'images_status']['trace_url'] = "https://langfuse.sergedc.com/" + "/".join(case_procesor_trace.get_trace_url().replace("://", "").split("/")[1:])
        else:
            cases_df.at[idx, 'images_status']['trace_url'] = None

        # Initialize steps_df and relevant_steps_df to ensure they exist for the finally block of span_process_steps
        step_iter = -1 # In case loop doesn't run

        span_process_steps = case_procesor_trace.span(
            name="process_steps_main_loop",
            input={"Docket": docket_number, "Court": court, "UpdateStepsOption": options.get('update_steps'), "InitialIPRelevantGoals": {"TM": ip_manager.is_goal_relevant('trademark'), "CR": ip_manager.is_goal_relevant('copyright'), "PT": ip_manager.is_goal_relevant('patent')}, "MissingStatus": ip_manager.get_missing_status()}
        )
        try:
            ### --- 3. Get Steps Info, Prioritize, and Process Iteratively --- ###
            log_message(f"🔄 Fetching and analysing relevance of steps for {docket_number}...", level='INFO')
            db_steps_df = pd.DataFrame()  # Initialize as empty DataFrame

            # In resume mode, try to load existing steps from the database first
            if current_processing_mode == 'resume':
                case_id_for_db_lookup = cases_df.at[idx, 'id']
                if case_id_for_db_lookup is not None:
                    try:
                        log_message(f"   Resume mode: Attempting to load existing steps from DB for case ID {case_id_for_db_lookup}...", level='INFO')
                        db_steps_df = get_table_from_GZ("tb_case_steps", where_clause=f"case_id = {case_id_for_db_lookup}", force_refresh=True)
                        if not db_steps_df.empty:
                            log_message(f"   Found {len(db_steps_df)} existing steps in DB for case ID {case_id_for_db_lookup}.", level='INFO')
                            # Ensure 'step_nb' from DB is string for consistent merging
                            # if 'step_nb' in db_steps_df.columns:
                                # db_steps_df['step_nb'] = db_steps_df['step_nb'].astype(str)
                            # else:
                            #     log_message("   Warning: 'step_nb' column missing in db_steps_df. Merge might be affected.", level='WARNING')
                            #     db_steps_df = pd.DataFrame() # Prevent merge with malformed df
                        else:
                            log_message(f"   No existing steps found in DB for case ID {case_id_for_db_lookup}.", level='INFO')
                    except Exception as e:
                        log_message(f"   Error loading existing steps from DB for resume mode: {e}. Proceeding without them.", level='WARNING')
                        db_steps_df = pd.DataFrame() # Ensure it's an empty df on error
                else:
                    log_message(f"   Resume mode: Case ID is None (new case?). Skipping load of existing steps from DB.", level='INFO')

            try:
                ## -- 3a. Get Full Steps DataFrame -- ##
                log_message(f"   Getting full steps DataFrame for {docket_number}...", level='DEBUG')
                steps_df, steps_fetch_success = Get_Case_Steps.get_case_steps(driver, docket_number, court, options, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_process_steps.id)
                steps_df['case_id'] = cases_df.at[idx, 'id']

                if not steps_fetch_success:
                    log_message(f"❌ Failed to fetch steps DataFrame for {docket_number}. Raising...", level='ERROR')
                    raise Exception("Step fetching failed")
                if steps_df is None or steps_df.empty:
                    log_message(f"⚠️ No steps found for {docket_number}. Abording...", level='WARNING')
                    return

                # Merge with DB steps if in resume mode and both DataFrames are available
                if current_processing_mode == 'resume' and not db_steps_df.empty and steps_df is not None and not steps_df.empty:
                    log_message(f"   Resume mode: Merging {len(steps_df)} newly fetched steps with {len(db_steps_df)} DB steps.", level='INFO')
                    cols_to_preserve_from_db = ['step_nb', 'proceeding_text_cn', 'files_downloaded', 'files_failed']
                    # Ensure db_steps_df only has the columns we want to merge and unique step_nb
                    db_steps_to_merge = db_steps_df[db_steps_df.columns.intersection(cols_to_preserve_from_db)].drop_duplicates(subset=['step_nb'], keep='last')

                    merged_df = pd.merge(steps_df, db_steps_to_merge, on='step_nb', how='left', suffixes=('', '_db') )

                    # Restore preserved columns, preferring DB version if it exists
                    merged_df['proceeding_text_cn'] = merged_df['proceeding_text_cn_db'].fillna(merged_df['proceeding_text_cn'])
                    merged_df['files_downloaded'] = merged_df['files_downloaded_db'].fillna(merged_df['files_downloaded'])
                    merged_df['files_failed'] = merged_df['files_failed_db'].fillna(merged_df['files_failed'])
                    merged_df['files_downloaded'] = merged_df['files_downloaded'].fillna(0)
                    merged_df['files_failed'] = merged_df['files_failed'].fillna(0)

                    # Clean up temporary '_db' columns
                    cols_to_drop_after_merge = [col for col in merged_df.columns if col.endswith('_db')]
                    merged_df.drop(columns=cols_to_drop_after_merge, inplace=True, errors='ignore')

                    steps_df = merged_df
                    log_message(f"   Merge complete. Resulting steps_df has {len(steps_df)} rows.", level='INFO')


                ### Step by step processing
                if current_processing_mode == 'resume' and ip_manager.are_all_relevant_goals_met():
                    log_message(f"   Skipping step processing for {docket_number} as all relevant goals are already met.", level='INFO')
                else:
                    log_message(f"✅ Found {len(steps_df)} steps, put in DataFrame with their Priority for {docket_number}.", level='INFO')

                    ## --  3b. Identify Prioritized Steps for Download -- ##
                    # Use numeric priority and is_downloadable flag
                    relevant_steps_df = steps_df[
                        (steps_df['priority'] != Get_Case_Steps.PRIORITY_OTHER) &
                        (steps_df['is_downloadable'] == True)
                    ].sort_values(by=['priority', 'row_index'])
                    relevant_steps_df["processed_already"] = False

                    # If in resume mode and we successfully loaded the ip_manager state,
                    # filter out steps that have already been processed
                    if current_processing_mode == 'resume' and 'steps_processed' in cases_df.at[idx, 'images_status']:
                        original_relevant_count = len(relevant_steps_df)

                        # Filter out steps that have already been processed
                        relevant_steps_df = relevant_steps_df[~relevant_steps_df['step_nb'].astype(str).isin(cases_df.at[idx, 'images_status']['steps_processed'])]

                        skipped_steps_count = original_relevant_count - len(relevant_steps_df)
                        log_message(f"   Resume mode: Skipping {skipped_steps_count} previously processed steps.", level='INFO')

                    log_message("---------------------------------------")
                    log_message(f"Relevant steps to be considered: \n {relevant_steps_df[['step_nb', 'priority_name', 'proceeding_text']]}", level='INFO')
                    log_message("---------------------------------------\n")


                    for step_iter, (step_row_nb, step_data) in enumerate(relevant_steps_df.iterrows()):
                        step_row_index = step_data['row_index']
                        steps_df.loc[step_row_nb, 'files_downloaded'] = 0
                        steps_df.loc[step_row_nb, 'files_failed'] = 0

                        step_nb = step_data.get('step_nb')
                        span_process_step = langfuse_client.span(
                            name=f"process_step_{int(float(step_nb)) if pd.notna(step_nb) else 'unknown'}", trace_id=trace_id, parent_observation_id=span_process_steps.id,
                            input={"StepNB": step_nb, "StepRowIndex": step_row_index, "StepPriority": step_data.get('priority_name', 'N/A'), "IPGoalsBeforeStep": {"TM": ip_manager.is_goal_met('trademark'), "CR": ip_manager.is_goal_met('copyright'), "PT": ip_manager.is_goal_met('patent')}}
                        )
                        try:
                            if ip_manager.are_all_step_processing_goals_met(): # Changed from are_all_relevant_goals_met
                                log_message(f"   All relevant IP goals met. Skipping further step processing.", level='INFO')
                                break

                            if step_data["processed_already"]:
                                continue

                            doc_date_filed = step_data.get('step_date_filed')

                            log_message(f"🔨🔨🔨 {step_iter+1}/{len(relevant_steps_df)}: Next highest priority step: {step_nb}, at row_index {step_row_index} (Priority: {step_data['priority_name']}).", level='INFO')

                            # if step_row_index is None: # Should not happen if relevant_steps_df is populated correctly
                            #     log_message(f"    No suitable, unattempted, downloadable, prioritized step found (step_row_index is None).", level='DEBUG')
                            #     break # of the while loop

                            ## --  3c. Download main document -- ##
                            main_pdf_path = None
                            downloaded_main_paths = [] # Renamed from downloaded_paths to avoid conflict

                            try:
                                steps_table_element = driver.find_element(By.CLASS_NAME, "SS_DataTable")
                                tr_element = steps_table_element.find_elements(By.TAG_NAME, 'tr')[step_row_index]
                                step_attachment_details = get_step_details(driver, tr_element, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_process_step.id)
                                if steps_df.loc[step_row_nb,'files_number'] != 1 + len(step_attachment_details):
                                    steps_df.loc[step_row_nb,'files_number'] = 1 + len(step_attachment_details)
                                log_message(f"        🎯 Attempting download main document (Using download_mode='main_only') for step {step_nb} (row {step_row_index})", level='INFO')
                                step_main_options = options.copy()
                                step_main_options['download_mode'] = 'main_only'

                                # --- Download Main Document ---
                                span_main_doc = langfuse_client.span(
                                    name=f"main_document", trace_id=trace_id, parent_observation_id=span_process_step.id,
                                    input={"StepNB": step_nb, "StepRowIndex": step_row_index, "DownloadMode": step_main_options.get('download_mode', 'N/A'), "NumAttachmentDetails": len(step_attachment_details)}
                                )

                                downloaded_main_paths, pacer_retrievals, newly_downloaded_main_count = Download_File.get_step_file(
                                    driver, tr_element, docket_number, court, cases_df.loc[idx, 'date_filed'], step_nb, doc_date_filed,
                                    options=step_main_options,
                                    langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_main_doc.id # Pass span_main_doc.id
                                )

                                # Update file download tracking
                                downloaded_count = len(downloaded_main_paths)
                                steps_df.loc[steps_df['row_index'] == step_row_index, 'files_downloaded'] += downloaded_count
                                steps_df.loc[steps_df['row_index'] == step_row_index, 'files_failed'] += 1 - downloaded_count

                                # Track total files downloaded and PACER retrievals
                                total_file += newly_downloaded_main_count
                                pacer_file += pacer_retrievals

                                if downloaded_main_paths and os.path.exists(downloaded_main_paths[0]):
                                    main_pdf_path = downloaded_main_paths[0]
                                    log_message(f"        ✅ Main document for Step {step_nb} available: {os.path.basename(main_pdf_path)} (Newly downloaded: {newly_downloaded_main_count})", level='INFO')
                                else:
                                    log_message(f"        ⚠️ No main document downloaded for step {step_nb}.", level='WARNING')

                            except IndexError:
                                log_message(f"        ❌ Error finding table row element for row_index {step_row_index}. Skipping step.", level='ERROR')
                                steps_df.loc[steps_df['row_index'] == step_row_index, 'files_failed'] += 1
                            except Exception as loop_err:
                                log_message(f"        ❌ Error processing step at row_index {step_row_index}: {loop_err}\n{traceback.format_exc()}", level='ERROR')
                                steps_df.loc[steps_df['row_index'] == step_row_index, 'files_failed'] += 1

                            if not main_pdf_path:
                                log_message("        ⚠️ No main PDFs downloaded, and hence no LLM analysis conducted.", level='WARNING')
                                relevant_steps_df.loc[step_data.name, "processed_already"] = True
                                if 'span_main_doc' in locals() and span_main_doc:
                                    span_main_doc.end(output={"MainPDFPath": None, "LLMTargetsCount": 0})
                                if 'span_process_step' in locals() and span_process_step:
                                    span_process_step.end(output={"MainPDFDownloaded": 'No'})
                                continue # To the next step

                            ##--  3c. LLM Analysis of main document PDF --##
                            log_message(f"        🧠 Analyzing main PDF with LLM for IP scope for step {step_nb}", level='INFO')
                            llm_targets = [] # Default
                            try:
                                # --- Prepare Associated Steps Context for LLM ---
                                associated_steps = []
                                if doc_date_filed:
                                    same_day_steps_df = relevant_steps_df[
                                        (relevant_steps_df['step_date_filed'] == doc_date_filed) &
                                        (relevant_steps_df['row_index'] != step_row_index)
                                    ].copy()
                                    associated_steps = same_day_steps_df[['step_nb', 'proceeding_text']].rename(columns={'proceeding_text': 'description'}).to_dict('records')
                                else:
                                    log_message(f"        Associated steps: Skipping associated steps context: Current step date is missing.", level='WARNING')

                                log_message(f"        Calling LLM with main document and info on {len(step_attachment_details)} attachments and {len(associated_steps)} associated steps.", level='DEBUG')
                                llm_targets = IPAnalysisLLM.analyze_pdf_for_ip(main_pdf_path, step_attachment_details, associated_steps, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_main_doc.id if 'span_main_doc' in locals() and span_main_doc else span_process_step.id)
                                ip_manager.update_values_from_llm(llm_targets)
                            except Exception as llm_err:
                                log_message(f"        ❌ Error during LLM analysis of main document: {llm_err}\n{traceback.format_exc()}", level='ERROR')

                            ##--  3d. Get IP using main document info (if applicable) --##
                            # Check Patent goals
                            patent_reg_nos_processed_main_doc_count = 0
                            if not ip_manager.is_goal_met('patent') and ip_manager._state["patent"]['reg_nos_status'] == "WholeSet":
                                needed_reg_nos_patent = list(ip_manager._state["patent"]["target_reg_nos"] - ip_manager._state["patent"]['found_reg_nos'])
                                if needed_reg_nos_patent:
                                    ProcessPictures.process_patent_registration_numbers(local_case_images_dir, cases_df, idx, needed_reg_nos_patent, ip_manager, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_main_doc.id if 'span_main_doc' in locals() else span_process_step.id)
                                    patent_reg_nos_processed_main_doc_count = len(needed_reg_nos_patent)
                            # Check Trademark goals
                            trademark_reg_nos_processed_main_doc_count = 0
                            if not ip_manager.is_goal_met('trademark') and ip_manager._state["trademark"]['reg_nos_status'] == "WholeSet":
                                needed_reg_nos_trademark = list(ip_manager._state["trademark"]["target_reg_nos"] - ip_manager._state["trademark"]['found_reg_nos'])
                                if needed_reg_nos_trademark:
                                    ProcessPictures.process_trademark_registration_numbers(local_case_images_dir, cases_df, idx, needed_reg_nos_trademark, ip_manager, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_main_doc.id if 'span_main_doc' in locals() else span_process_step.id)
                                    trademark_reg_nos_processed_main_doc_count = len(needed_reg_nos_trademark)
                            # Check Copyright goals
                            copyright_reg_nos_processed_main_doc_count = 0
                            if not ip_manager.is_goal_met('copyright') and \
                                len(ip_manager._state["copyright"]['target_attachment_indices']) == 0 and \
                                len(ip_manager._state["copyright"]['target_step_nbs']) == 0 and \
                                ip_manager._state["copyright"]['target_main_doc'] == True:
                                needed_reg_nos_copyright = list(ip_manager._state["copyright"]["target_reg_nos"] - ip_manager._state["copyright"]['found_reg_nos'])
                                if needed_reg_nos_copyright:  # Only process if numbers were actually found by LLM and not yet found by scraping
                                    # Returns reg_nos not found in PDF, which might need further processing by get_copyright_art_info if called later
                                    Copyrights_Main_PDF.process_copyright_main_pdf(cases_df, idx, local_case_images_dir, main_pdf_path, needed_reg_nos_copyright, ip_manager, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_main_doc.id if 'span_main_doc' in locals() else span_process_step.id)
                                    copyright_reg_nos_processed_main_doc_count = len(needed_reg_nos_copyright)

                            span_main_doc.end(output={"MainPDFPath": os.path.basename(main_pdf_path) if main_pdf_path else 'None', "LLMTargetsCount": len(llm_targets), "PatentRegNosProcessedMainDoc": patent_reg_nos_processed_main_doc_count, "TrademarkRegNosProcessedMainDoc": trademark_reg_nos_processed_main_doc_count, "CopyrightRegNosProcessedMainDoc": copyright_reg_nos_processed_main_doc_count})

                            ##-- 3e. Process LLM-identified locations (Attachments & Other Steps) --##
                            # --- 3e.1 Process Targeted Other Steps ---
                            # Get targeted locations for unmet goals (attachement and steps)
                            unmet_attachment_indices, unmet_step_nbs = ip_manager.get_targeted_locations_for_unmet_goals()
                            file_path_for_llm_to_look_at = []
                            if unmet_step_nbs:
                                # Use the ID of the 'process_llm_identified_locations_step_X' span as the parent
                                span_target_steps = langfuse_client.span(
                                    name=f"1. targeted_other_steps: {unmet_step_nbs}", trace_id=trace_id, parent_observation_id=span_process_step.id,
                                    input={"UnmetStepNBsToTarget": unmet_step_nbs if 'unmet_step_nbs' in locals() else 'N/A', "CurrentStepNB": step_nb}
                                )
                                try:
                                    log_message(f"        🎯 Process Targeted Steps identified by LLM for Step {step_nb}  - Target Children Steps:{unmet_step_nbs}. Starting...", level='INFO')
                                    for target_step_nb_str in unmet_step_nbs:
                                        span_target_step = langfuse_client.span(
                                            name=f"targeted_other_step: {target_step_nb_str}", trace_id=trace_id, parent_observation_id=span_target_steps.id,
                                            input={"TargetStepNB": target_step_nb_str, "TargetStepRowIndex": target_step_row_index if 'target_step_row_index' in locals() else 'N/A'}
                                        )
                                        try:
                                            # Find the target step in the main steps_df
                                            target_step_row = steps_df[steps_df['step_nb'] == target_step_nb_str]

                                            if not target_step_row.empty:
                                                target_step_data = target_step_row.iloc[0]
                                                target_step_row_index = target_step_data['row_index']  # e.g. if we are looging for step 15, which is the 17th row in the table due to 2 steps without number => table[0] is the title row, and table[17] is the 17th row, the 15th step
                                                target_doc_date_filed = target_step_data['step_date_filed']

                                                # Check if this target step was already processed in a previous iteration of the outer loop
                                                target_already_processed = False
                                                if target_step_row_index in relevant_steps_df.index:
                                                    target_already_processed = relevant_steps_df.loc[target_step_row_index, 'processed_already']

                                                if target_already_processed:
                                                    log_message(f"      Target step {target_step_nb_str} was already processed. Skipping re-download.", level='INFO')
                                                    continue # Skip to next target step

                                                try:
                                                    # Get the HTML element for the *target* step row
                                                    target_tr_element = steps_table_element.find_elements(By.TAG_NAME, 'tr')[target_step_row_index] # e.g. if we are looging for step 15, which is the 17th row in the table due to 2 steps without number => table[0] is the title row, and table[17] is the 17th row, the 15th step

                                                    # Download the main document of the target step
                                                    target_step_options = options.copy()
                                                    target_step_options['download_mode'] = 'main_only' # Assuming main doc is sufficient

                                                    log_message(f"        Downloading main document for target step {target_step_nb_str}...", level='INFO')

                                                    try:
                                                        # --- Download Target Step Main Document ---
                                                        downloaded_target_paths, target_pacer_retrievals, newly_downloaded_target_count = Download_File.get_step_file(
                                                            driver, target_tr_element, docket_number, court, cases_df.loc[idx, 'date_filed'],
                                                            target_step_nb_str, target_doc_date_filed,
                                                            options=target_step_options, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_target_step.id
                                                        )

                                                        target_downloaded_count = len(downloaded_target_paths)
                                                        steps_df.loc[steps_df['row_index'] == target_step_row_index, 'files_downloaded'] += target_downloaded_count
                                                        steps_df.loc[steps_df['row_index'] == target_step_row_index, 'files_failed'] += 1 - target_downloaded_count  # Expecting only 1 main file

                                                        # Update tracking counters
                                                        total_file += newly_downloaded_target_count
                                                        pacer_file += target_pacer_retrievals

                                                        log_message(f"        Target step {target_step_nb_str} download tracking: Newly Downloaded={newly_downloaded_target_count}, Failed={1 - target_downloaded_count}, PACER={target_pacer_retrievals}", level='DEBUG')

                                                    except Exception as target_dl_err:
                                                        log_message(f"        ❌ Error downloading target step {target_step_nb_str}: {target_dl_err}", level='ERROR')
                                                        # Increment failed count for the target step if download fails
                                                        steps_df.loc[steps_df['row_index'] == target_step_row_index, 'files_failed'] += 1 # Assume 1 expected file failed
                                                        downloaded_target_paths = [] # Ensure it's an empty list on failure

                                                    if downloaded_target_paths:
                                                        try:
                                                            target_pdf_path = downloaded_target_paths[0]
                                                            log_message(f"        ✅ Finished Main document download for Target Step {target_step_nb_str}: {os.path.basename(target_pdf_path)}", level='INFO')

                                                            # Process the downloaded PDF from the target step
                                                            ocr_data, is_rubbish, need_llm_file_paths = ProcessPictures.process_attachement_pdfs(
                                                                [target_pdf_path], cases_df, idx, local_case_dir,
                                                                f"step_{target_step_nb_str}", # Unique identifier for the target step's main doc
                                                                copyright_template1_hash, copyright_template1_size, certainty="high", ip_manager=ip_manager,
                                                                langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_target_step.id
                                                            )
                                                            file_path_for_llm_to_look_at.extend(need_llm_file_paths)
                                                        except Exception as target_proc_err:
                                                            log_message(f"        ❌ Error in process_attachement_pdfs for processing target step {target_step_nb_str}: {target_proc_err}\n{traceback.format_exc()}", level='ERROR')

                                                    else:
                                                        log_message(f"        ⚠️ No document downloaded for target step {target_step_nb_str}, skipping PDF processing.", level='WARNING')


                                                    # Mark the target step as processed *in the relevant_steps_df*
                                                    if target_step_row_index in relevant_steps_df.index:
                                                        relevant_steps_df.loc[target_step_row_index, 'processed_already'] = True
                                                        log_message(f"        Marked target step {target_step_nb_str} (row_index {target_step_row_index}) as processed.", level='INFO')
                                                    else:
                                                        log_message(f"        ⚠️ Failed to download document for target step {target_step_nb_str}.", level='WARNING')

                                                except Exception as target_proc_err:
                                                    log_message(f"        ❌ Error processing target step {target_step_nb_str}: {target_proc_err}\n{traceback.format_exc()}", level='ERROR')

                                            else:
                                                log_message(f"        ⚠️ Target step number {target_step_nb_str} not found in steps_df.", level='WARNING')
                                        finally:
                                            span_target_step.end(output={"TargetStepDownloaded": 'Yes' if 'downloaded_target_paths' in locals() and downloaded_target_paths else 'No', "ProcessedAsRubbish": is_rubbish if 'is_rubbish' in locals() else 'N/A', "NeedLLMFilePathsCount": len(need_llm_file_paths) if 'need_llm_file_paths' in locals() else 0})
                                finally:
                                    span_target_steps.end(output={"TargetStepsProcessedCount": len([s for s in (unmet_step_nbs if 'unmet_step_nbs' in locals() else []) if s in (steps_df['step_nb'].tolist() if 'steps_df' in locals() and steps_df is not None else [])]), "FilePathsForLLMFromTargetedSteps": len([fp for fp in (file_path_for_llm_to_look_at if 'file_path_for_llm_to_look_at' in locals() else []) if 'step_' in fp]), "IPGoalsAfterTargetedSteps": {"TM": ip_manager.is_goal_met('trademark') if 'ip_manager' in locals() else 'N/A', "CR": ip_manager.is_goal_met('copyright') if 'ip_manager' in locals() else 'N/A', "PT": ip_manager.is_goal_met('patent') if 'ip_manager' in locals() else 'N/A'}})


                            # --- 3e.2 Process Targeted Attachments (from current step) ---
                            if unmet_attachment_indices:
                                # Use the ID of the 'process_llm_identified_locations_step_X' span as the parent
                                span_target_attachments = langfuse_client.span(
                                    name=f"2. targeted_attachments: {unmet_attachment_indices}", trace_id=trace_id, parent_observation_id=span_process_step.id,
                                    input={"UnmetAttachmentIndicesToTarget": unmet_attachment_indices if 'unmet_attachment_indices' in locals() else 'N/A', "CurrentStepNB": step_nb}
                                )
                                try:
                                    log_message(f"        🎯 Process Targeted Attachments identified by LLM for Step {step_nb}  - Attachement {unmet_attachment_indices}. Starting...", level='INFO')
                                    option_attachements = options.copy()
                                    option_attachements['download_mode'] = "attachements"

                                    # Download all targeted attachments for this step at once if possible
                                    target_attachment_indices = sorted(list(unmet_attachment_indices)) # Ensure order for processing

                                    try:
                                        # --- Download Targeted Attachments ---
                                        downloaded_paths, attachments_pacer_retrievals, newly_downloaded_targeted_attach_count = Download_File.get_step_file(
                                            driver, tr_element, docket_number, court, cases_df.loc[idx, 'date_filed'], step_nb, doc_date_filed,
                                            options=option_attachements,
                                            target_attachment_indices=target_attachment_indices, # Pass the list of 1-based indices
                                            langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_target_attachments.id
                                        )

                                        # Update tracking counters
                                        downloaded_count = len(downloaded_paths)
                                        steps_df.loc[steps_df['row_index'] == step_row_index, 'files_downloaded'] += downloaded_count
                                        steps_df.loc[steps_df['row_index'] == step_row_index, 'files_failed'] += (len(target_attachment_indices) - downloaded_count)


                                        # Track total files downloaded and PACER retrievals
                                        total_file += newly_downloaded_targeted_attach_count
                                        pacer_file += attachments_pacer_retrievals

                                        log_message(f"        Targeted attachments download tracking for step {step_nb}: Expected={len(target_attachment_indices)}, Newly Downloaded={newly_downloaded_targeted_attach_count}, Failed={len(target_attachment_indices) - downloaded_count}, PACER={attachments_pacer_retrievals}", level='DEBUG')

                                    except Exception as target_att_dl_err:
                                        log_message(f"        ❌ Error downloading targeted attachments {target_attachment_indices}: {target_att_dl_err}", level='ERROR')
                                        # Increment failed count for the current step if download fails
                                        steps_df.loc[steps_df['row_index'] == step_row_index, 'files_failed'] += len(target_attachment_indices) # Assume all failed if exception
                                        downloaded_paths = [] # Ensure empty list on error

                                    if downloaded_paths:
                                        log_message(f"        ✅ Downloaded targeted attachments for Step {step_nb}: {[os.path.basename(downloaded_path) for downloaded_path in downloaded_paths]}", level='INFO')

                                        log_message("**********************")
                                        try:
                                            ocr_data, is_rubbish, need_llm_file_paths = ProcessPictures.process_attachement_pdfs(downloaded_paths, cases_df, idx, local_case_dir, f"att_batch_{step_nb}",copyright_template1_hash, copyright_template1_size, certainty="high", ip_manager=ip_manager, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_target_attachments.id)
                                            file_path_for_llm_to_look_at.extend(need_llm_file_paths)
                                        except Exception as target_att_proc_err:
                                            log_message(f"        ❌ Error in process_attachement_pdfs for processing targeted attachments: {target_att_proc_err}\n{traceback.format_exc()}", level='ERROR')
                                        log_message("**********************")
                                    else:
                                        log_message(f"        ⚠️ No files downloaded for targeted attachment indices: {target_attachment_indices}, skipping PDF processing.", level='WARNING')

                                finally:
                                    span_target_attachments.end(output={"TargetAttachmentsDownloadedCount": len(downloaded_paths) if 'downloaded_paths' in locals() else 0, "ExpectedTargetAttachments": len(target_attachment_indices) if 'target_attachment_indices' in locals() else 0, "FilePathsForLLMFromTargetedAttachments": len([fp for fp in (file_path_for_llm_to_look_at if 'file_path_for_llm_to_look_at' in locals() else []) if 'att_batch_' in fp]), "IPGoalsAfterTargetedAttachments": {"TM": ip_manager.is_goal_met('trademark') if 'ip_manager' in locals() else 'N/A', "CR": ip_manager.is_goal_met('copyright') if 'ip_manager' in locals() else 'N/A', "PT": ip_manager.is_goal_met('patent') if 'ip_manager' in locals() else 'N/A'}})

                            # --- 3e.3 Process Other Attachments (if goals still unmet and no specific targets were processed) ---
                            # Only run this if goals remain unmet after processing targeted locations.
                            if not ip_manager.are_all_relevant_goals_met():
                                # Use the ID of the 'process_llm_identified_locations_step_X' span as the parent
                                span_other_attachments = langfuse_client.span(
                                    name=f"3. other_attachments",
                                    trace_id=trace_id,
                                    parent_observation_id=span_process_step.id,
                                    input={"CurrentStepNB": step_nb, "GoalsStillUnmet": not ip_manager.are_all_relevant_goals_met() if 'ip_manager' in locals() else 'N/A', "TotalAttachmentDetails": len(step_attachment_details) if 'step_attachment_details' in locals() else 'N/A'}
                                )
                                try:
                                    log_message(f"        🎯 Processing other non-targeted attachments for Step {step_nb} as goals remain unmet...", level='INFO')
                                    option_attachements = options.copy()
                                    option_attachements['download_mode'] = "attachements"

                                    for step_attachment_detail in step_attachment_details:
                                        # Check if this attachment was already targeted by the LLM
                                        current_attachment_index = step_attachment_detail["index"] # This is 1-based
                                        current_attachment_text = step_attachment_detail["name"] # This is the text description of the attachment
                                        # Check if this attachment was already targeted by the LLM (using unmet_attachment_indices from earlier)
                                        if current_attachment_index in unmet_attachment_indices or "civil cover sheet" in current_attachment_text.lower() or "schedule a" in current_attachment_text.lower() or "summons form" in current_attachment_text.lower():
                                            log_message(f"           Skipping attachment index {current_attachment_index} as it was already targeted.", level='DEBUG')
                                            continue # Skip if already processed as a target

                                        is_rubbish = False
                                        log_message(f"           Attempting download for step {step_nb} non-targeted attachment index {current_attachment_index}...", level='DEBUG')

                                        span_other_attachment = langfuse_client.span(
                                            name=f"Step {step_nb} - Attachment {current_attachment_index}",
                                            trace_id=trace_id,
                                            parent_observation_id=span_other_attachments.id,
                                            input={"StepNB": step_nb, "AttachmentIndex": current_attachment_index if 'current_attachment_index' in locals() else 'N/A', "AttachmentName": current_attachment_text if 'current_attachment_text' in locals() else 'N/A'}
                                        )
                                        try:
                                            try:
                                                # --- Download Non-Targeted Attachment ---
                                                downloaded_paths, other_pacer_retrievals, newly_downloaded_other_attach_count = Download_File.get_step_file(
                                                    driver, tr_element, docket_number, court, cases_df.loc[idx, 'date_filed'], step_nb, doc_date_filed,
                                                    options=option_attachements,
                                                    target_attachment_indices=[current_attachment_index], # Download one by one
                                                    langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_other_attachment.id
                                                )

                                                # Update tracking counters
                                                other_downloaded_count = len(downloaded_paths)
                                                steps_df.loc[steps_df['row_index'] == step_row_index, 'files_downloaded'] += other_downloaded_count
                                                steps_df.loc[steps_df['row_index'] == step_row_index, 'files_failed'] += 1 - other_downloaded_count

                                                # Track total files downloaded and PACER retrievals
                                                total_file += newly_downloaded_other_attach_count
                                                pacer_file += other_pacer_retrievals


                                            except Exception as non_target_dl_err:
                                                log_message(f"           ❌ Error downloading non-targeted attachment {current_attachment_index}: {non_target_dl_err}", level='ERROR')
                                                # Increment failed count for the current step if download fails
                                                steps_df.loc[steps_df['row_index'] == step_row_index, 'files_failed'] += 1 # Assume 1 failed
                                                downloaded_paths = [] # Ensure empty list on error


                                            if downloaded_paths:
                                                log_message(f"           Finished downloading Non-targeted attachment {current_attachment_index} for Step {step_nb}: {os.path.basename(downloaded_paths[0])}", level='DEBUG')

                                                try:
                                                    attachment_path = downloaded_paths[0]
                                                    # Assume ProcessPictures.process_downloaded_pdfs will be updated to use ip_manager
                                                    ocr_data, is_rubbish, need_llm_file_paths = ProcessPictures.process_attachement_pdfs(
                                                        [attachment_path], cases_df, idx, local_case_dir,
                                                        f"att_{current_attachment_index}", # Unique identifier using 1-based index
                                                        copyright_template1_hash, copyright_template1_size, certainty="low", ip_manager=ip_manager,
                                                        langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_other_attachment.id
                                                    )
                                                    file_path_for_llm_to_look_at.extend(need_llm_file_paths)

                                                    log_message(f"           ✅ Processed non-targeted attachment: {attachment_path}", level='INFO')
                                                except Exception as non_target_proc_err:
                                                    log_message(f"           ❌ Error in process_attachement_pdfs for processing non-targeted attachment {current_attachment_index}: {non_target_proc_err}\n{traceback.format_exc()}", level='ERROR')

                                            else:
                                                log_message(f"           ⚠️ No file downloaded for non-targeted attachment index {current_attachment_index}, skipping PDF processing.", level='WARNING')
                                        finally:
                                            span_other_attachment.end(output={"AttachmentDownloaded": 'Yes' if 'downloaded_paths' in locals() and downloaded_paths else 'No', "ProcessedAsRubbish": is_rubbish if 'is_rubbish' in locals() else 'N/A', "NeedLLMFilePathAdded": 'Yes' if 'need_llm_file_paths' in locals() and need_llm_file_paths else 'No'})

                                        if is_rubbish:
                                            log_message(f"           🚫 Rubbish report found in non-targeted attachment index {current_attachment_index}. Stopping check for this step.", level='WARNING')
                                            break # Stop checking other attachments for this step if rubbish found
                                finally:
                                    span_other_attachments.end(output={"OtherAttachmentsProcessedCount": sum(1 for detail in (step_attachment_details if 'step_attachment_details' in locals() else []) if detail['index'] not in (unmet_attachment_indices if 'unmet_attachment_indices' in locals() else []) and 'civil cover sheet' not in detail['name'].lower() and 'schedule a' not in detail['name'].lower()), "RubbishFoundInOther": is_rubbish if 'is_rubbish' in locals() else 'N/A', "FilePathsForLLMFromOtherAttachments": len([fp for fp in (file_path_for_llm_to_look_at if 'file_path_for_llm_to_look_at' in locals() else []) if 'att_' in fp and 'att_batch' not in fp]), "IPGoalsAfterOtherAttachments": {"TM": ip_manager.is_goal_met('trademark') if 'ip_manager' in locals() else 'N/A', "CR": ip_manager.is_goal_met('copyright') if 'ip_manager' in locals() else 'N/A', "PT": ip_manager.is_goal_met('patent') if 'ip_manager' in locals() else 'N/A'}})

                            else:
                                log_message(f"        Skipping processing of other non-targeted attachments for step {step_nb} (goals met or none relevant).", level='INFO')

                            # --- 3f - Ask LLM to look into the PDF where we did not find anything
                            if not ip_manager.are_all_relevant_goals_met() and file_path_for_llm_to_look_at:
                                span_analyze_unresolved_pdfs = langfuse_client.span(
                                    name=f"analyze_unresolved_pdf_for_reg", trace_id=trace_id, parent_observation_id=span_process_step.id,
                                    input={"StepNB": step_nb, "NumPDFsToAnalyze": len(file_path_for_llm_to_look_at) if 'file_path_for_llm_to_look_at' in locals() else 0, "GoalsStillUnmet": not ip_manager.are_all_relevant_goals_met() if 'ip_manager' in locals() else 'N/A'}
                                )
                                try:
                                    log_message(f"        🧠 Analyzing: targeted steps, targeted attachements and other attachements that did not lead to IP being found, with LLM for IP regitration number for step {step_nb}", level='INFO')
                                    llm_targets_from_unresolved = IPAnalysisLLM.analyze_pdf_for_ip(file_path_for_llm_to_look_at, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_analyze_unresolved_pdfs.id)
                                    # Update the manager with the results from the LLM
                                    ip_manager.update_values_from_llm(llm_targets_from_unresolved)
                                finally:
                                    span_analyze_unresolved_pdfs.end(output={"LLMTargetsFromUnresolvedCount": llm_targets_from_unresolved if 'llm_targets' in locals() and llm_targets else 0, "IPGoalsAfterUnresolvedAnalysis": {"TM": ip_manager.is_goal_met('trademark') if 'ip_manager' in locals() else 'N/A', "CR": ip_manager.is_goal_met('copyright') if 'ip_manager' in locals() else 'N/A', "PT": ip_manager.is_goal_met('patent') if 'ip_manager' in locals() else 'N/A'}})


                            # --- 3g Get the Reg_no found in 3f as well as the Reg_no found in complaint (but not whole set and not found in exhibit)
                            if not ip_manager.are_all_relevant_goals_met():
                                span_process_ip_outstanding = langfuse_client.span(
                                    name=f"process_outstanding_reg_nos", trace_id=trace_id, parent_observation_id=span_process_step.id,
                                    input={"StepNB": step_nb, "PatentGoalMet": ip_manager.is_goal_met('patent') if 'ip_manager' in locals() else 'N/A', "TrademarkGoalMet": ip_manager.is_goal_met('trademark') if 'ip_manager' in locals() else 'N/A'}
                                )
                                try:
                                    # Check Patent goals and process numbers if needed
                                    if not ip_manager.is_goal_met('patent'):
                                        needed_reg_nos = list(ip_manager._state["patent"]["target_reg_nos"] - ip_manager._state["patent"]['found_reg_nos'])
                                        if needed_reg_nos: # Only process if numbers were actually found by LLM and not yet found by scraping
                                            ProcessPictures.process_patent_registration_numbers(local_case_images_dir, cases_df, idx, needed_reg_nos, ip_manager, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_process_ip_outstanding.id)
                                    # Check Trademark goals and process numbers if needed
                                    if not ip_manager.is_goal_met('trademark'):
                                        needed_reg_nos = list(ip_manager._state["trademark"]["target_reg_nos"] - ip_manager._state["trademark"]['found_reg_nos'])
                                        if needed_reg_nos: # Only process if numbers were actually found by LLM and not yet found by scraping
                                            ProcessPictures.process_trademark_registration_numbers(local_case_images_dir, cases_df, idx, needed_reg_nos, ip_manager, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_process_ip_outstanding.id)

                                    if not ip_manager.is_goal_met('copyright'):
                                        needed_reg_nos = list(ip_manager._state["copyright"]["target_reg_nos"] - ip_manager._state["copyright"]['found_reg_nos'])
                                        if needed_reg_nos: # Only process if numbers were actually found by LLM and not yet found by scraping
                                            # Call get_copyright_art_info for copyright numbers still needed. It looks at the main_pdf_path and usco to get Artist Name and Art name.
                                            Copyrights_UCO.get_copyright_art_info(cases_df, idx, needed_reg_nos, ip_manager, pdf_path_context=main_pdf_path, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_process_ip_outstanding.id)

                                finally:
                                    span_process_ip_outstanding.end(output={"PatentsProcessedOutstanding": len(needed_reg_nos) if 'ip_manager' in locals() and not ip_manager.is_goal_met('patent') and 'needed_reg_nos' in locals() else 0, "TrademarksProcessedOutstanding": len(needed_reg_nos) if 'ip_manager' in locals() and not ip_manager.is_goal_met('trademark') and 'needed_reg_nos' in locals() else 0, "IPGoalsAfterOutstandingProcessing": {"TM": ip_manager.is_goal_met('trademark') if 'ip_manager' in locals() else 'N/A', "PT": ip_manager.is_goal_met('patent') if 'ip_manager' in locals() else 'N/A'}})


                            # Mark the *current* step_data (from the outer loop) as processed
                            # Ensure this happens regardless of whether attachments/steps were processed

                            relevant_steps_df.loc[step_data.name, "processed_already"] = True

                            # Record the step as processed in the ip_manager
                            if step_nb is not None and "steps_processed" in cases_df.at[idx, 'images_status'] and step_nb not in cases_df.at[idx, 'images_status']['steps_processed']:
                                cases_df.at[idx, 'images_status']['steps_processed'].append(step_nb)

                            log_message(f"        Marked current step {step_nb} (row_index {step_row_index}) as processed.", level='INFO')
                            log_message(f"-------------------------------------------------------------\n")
                        finally: # for span_process_step
                            # Construct output for span_process_step carefully
                            _llm_targets_output = llm_targets if 'llm_targets' in locals() and llm_targets else []
                            _unmet_attach_indices_output = unmet_attachment_indices if 'unmet_attachment_indices' in locals() else []
                            _unmet_step_nbs_output = unmet_step_nbs if 'unmet_step_nbs' in locals() else []
                            _file_path_llm_output = file_path_for_llm_to_look_at if 'file_path_for_llm_to_look_at' in locals() else []
                            span_process_step.end(output={"MainDocDownloaded": 'Yes' if 'main_pdf_path' in locals() and main_pdf_path else 'No', "LLMTargetsIdentifiedCount": len(_llm_targets_output), "UnmetAttachIndicesAfterMainCount": len(_unmet_attach_indices_output), "UnmetStepNBsAfterMainCount": len(_unmet_step_nbs_output), "FilesForLLMRegAnalysisCount": len(_file_path_llm_output), "IPGoalsAfterStep": {"TM": ip_manager.is_goal_met('trademark'), "CR": ip_manager.is_goal_met('copyright'), "PT": ip_manager.is_goal_met('patent')}, "MissingStatus": ip_manager.get_missing_status()})

            except Exception as step_proc_err:
                log_message(f"        ❌ Error during steps fetching/processing for {docket_number}: {step_proc_err}\n{traceback.format_exc()}", level='CRITICAL')
                # This error might affect final_status, handled by the main try-except of this function.
                # If steps_df is crucial and fails here, it might need to set a specific failure status.
                # For now, let the main handler catch it.
            # Ensure relevant variables are defined for the finally block below
                if 'steps_df' not in locals(): steps_df = pd.DataFrame()
                if 'relevant_steps_df' not in locals(): relevant_steps_df = pd.DataFrame()
                if 'steps_fetch_success' not in locals(): steps_fetch_success = False
                if 'step_iter' not in locals(): step_iter = -1


        finally: # for span_process_steps
            final_ip_manager_summary_dict = {
                'TM': ip_manager.is_goal_met('trademark'),
                'CR': ip_manager.is_goal_met('copyright'),
                'PT': ip_manager.is_goal_met('patent')
            } if 'ip_manager' in locals() else {} # ip_manager should be defined
            span_process_steps.end(output={"StepsFetchSuccess": steps_fetch_success if 'steps_fetch_success' in locals() else False, "TotalStepsFetched": len(steps_df) if 'steps_df' in locals() and steps_df is not None else 0, "RelevantStepsToProcess": len(relevant_steps_df) if 'relevant_steps_df' in locals() and relevant_steps_df is not None else 0, "IterationsAttempted": step_iter + 1 if 'step_iter' in locals() else 0, "FinalIPManagerStateSummary": final_ip_manager_summary_dict, "AllRelevantStepProcessingGoalsMet": ip_manager.are_all_step_processing_goals_met() if 'ip_manager' in locals() else 'N/A', "AllOverallGoalsMet": ip_manager.are_all_relevant_goals_met() if 'ip_manager' in locals() else 'N/A'})


        #
        span_finalize_pictures = case_procesor_trace.span(
            name="finalize_picture_processing",
            input={"Case": docket_number, "PlaintiffID": cases_df.loc[idx,'plaintiff_id'] if idx is not None and idx in cases_df.index and 'plaintiff_id' in cases_df.columns else 'N/A', "LocalCaseDir": os.path.basename(local_case_dir) if local_case_dir else 'N/A'}
        )
        try:
            ### 4. Finalize Picture Processing: Chinese Website, Copyright Google and By Name + Deduplication, Resizing and Plaintiff View Creation
            log_message(f"🏁 Finalizing picture processing results for case: {docket_number}", level='INFO')
            # Chinese Website, Copyright Google and By Name - Now split into multiple calls
            log_message(f"  Checking if secondary IP search is needed for case {docket_number}...", level='INFO')

            if len(steps_df) == 1 and "closed" in steps_df['proceeding_text'].iloc[0].lower():
                log_message(f"  ⚠️  The case was administratively closed in step 1.", level='INFO')
                cases_df.loc[idx, 'file_status'] = "Admin Closed"
            elif not ip_manager.are_all_relevant_goals_met():
                log_message(f"  ⛏ Some IP goals unmet for case {docket_number}, performing secondary searches...", level='INFO')
                results = ProcessPictures.ChineseWebsites(cases_df, plaintiff_df, idx, local_case_images_dir, ip_manager, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_finalize_pictures.id)

                if not ip_manager.is_goal_met('copyright'):
                    # asyncio.run(ProcessPicturesCopyrights.get_copyright_images_from_google(cases_df, idx, local_case_images_dir, ip_manager, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_finalize_pictures.id))
                    # ip_manager.clear_pictures_not_found('copyright')
                    pass
                else:
                    log_message(f"  Copyright goal met or not relevant for case {docket_number}. Skipping Google image search for copyrights.", level='INFO')

                # Conditional logic for SearchByPlaintiffName
                if not ip_manager.are_all_relevant_goals_met() and cases_df.loc[idx].get("plaintiff_id") != 9:
                    log_message(f"  ⛏ Still some IP goals unmet for case {docket_number}, searching by plaintiff name...", level='INFO')
                    ProcessPictures.SearchByPlaintiffName(cases_df, plaintiff_df, idx, local_case_dir, local_case_images_dir, ip_manager, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_finalize_pictures.id)
                elif cases_df.loc[idx].get("plaintiff_id") == 9:
                    log_message(f"  Skipping search by plaintiff name for case {docket_number} because plaintiff_id == 9 (Unidentified Claimant).", level='INFO')
                else:
                    log_message(f"  Skipping search by plaintiff name for case {docket_number} (Goals met or Unidentified Claimant).", level='INFO')
            else:
                log_message(f"  All relevant IP goals met for case {docket_number} before secondary search. Skipping secondary searches.", level='INFO')


            # if ip_found: if no IP is found, the trademarks are already deduplicate and there are no images in the image folder (as it was emptied earlier)
            Trademarks_RegNo.deduplicate_all_trademarks(cases_df, idx, local_case_images_dir, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_finalize_pictures.id)

            ProcessPictures.create_resized_images(cases_df, idx, local_case_dir, local_case_images_dir, langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_finalize_pictures.id)

            ProcessPictures.create_plaintiff_images_view(plaintiff_df, local_case_dir, cases_df.loc[idx,'plaintiff_id'], langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_finalize_pictures.id)

        except Exception as e: # Error in finalize_picture_processing
            log_message(f"❌ Error during picture processing finalization for case {docket_number}: {e}\n{traceback.format_exc()}", level='ERROR')
        finally:
            span_finalize_pictures.end(output={"IPManagerStateAtFinalization": ip_manager.get_final_state() if 'ip_manager' in locals() else 'N/A'})


        span_parallel_tasks = case_procesor_trace.span(
            name="AI tasks and Uploads",
            input={
                "UploadFilesOption": options.get('upload_files', False),
                "RunPlaintiffOverview": options.get('run_plaintiff_overview', False),
                "RunSummaryTranslation": options.get('run_summary_translation', False),
                "RunStepTranslation": options.get('run_step_translation', False),
                "LocalCaseDir": os.path.basename(local_case_dir) if local_case_dir else 'N/A',
                "RemoteNASDir": os.path.basename(remote_nas_dir) if 'remote_nas_dir' in locals() else 'N/A',
                "StepsDfEmpty": steps_df.empty if 'steps_df' in locals() else True
            }
        )
        try:
            # Prepare all tasks that can run in parallel
            parallel_tasks = []


            if options.get('upload_files_nas', False):
                # 1. NAS Upload Task
                log_message(f"☁️ Preparing file uploads for case: {docket_number}", level='INFO')
                local_pdf_count = sum([len([f for f in files if f.lower().endswith('.pdf')]) for _, _, files in os.walk(local_case_dir)])

                if not ('remote_pdf_count' in locals() and remote_pdf_count == local_pdf_count):
                    log_message(f"📤 Preparing to upload files from {local_case_dir} to NAS path: {remote_nas_dir}", level='INFO')
                    # Create a NAS connection to be used by the task
                    nas = NASConnection()
                    parallel_tasks.append(('nas_upload', lambda: nas.ssh_local_to_nas(local_folder=local_case_dir, remote_folder=remote_nas_dir)))
                else:
                    log_message(f"Skipping NAS upload for case {docket_number} as local PDF count ({local_pdf_count}) = remote PDF count ({remote_pdf_count}).", level='INFO')

            # 2. COS Upload Task
            if options.get('upload_files_cos', False):
                if len(os.listdir(local_case_images_dir)) > 0:
                    log_message(f"☁️ Preparing to upload images to Tencent COS for case {docket_number}", level='INFO')
                    parallel_tasks.append(('cos_upload', lambda: send_pictures_to_cos(cases_df.loc[[idx]])))

            # 3. Summary Translation Task
            # if options.get('run_plaintiff_overview', False):

            if options.get('run_summary_translation', False):
                log_message(f"🧠 Preparing Summary Translation for case {docket_number}", level='INFO')
                parallel_tasks.append(('summary_translation', lambda: get_ai_summary_translations(cases_df, [idx], langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_parallel_tasks.id)))

            # 4. Steps Translation Task
            if options.get('run_step_translation', False):
                if not steps_df.empty:
                    log_message(f"🧠 Preparing Steps Translation for case {docket_number}", level='INFO')
                    # We need to wrap the async function in a synchronous function that runs the event loop
                    parallel_tasks.append(('steps_translation', lambda: asyncio.run(translate_steps_for_a_case(steps_df, cases_df.loc[idx, 'id'], langfuse_parent_trace_id=trace_id, langfuse_parent_observation_id=span_parallel_tasks.id))))
                else:
                    log_message(f"⚠️ Skipping Steps Translation for case {docket_number} due to missing steps data.", level='WARNING')

            # Execute all tasks concurrently
            if parallel_tasks:
                log_message(f"🚀 Executing {len(parallel_tasks)} parallel tasks for case {docket_number}: {[name for name, _ in parallel_tasks]}", level='INFO')
                with ThreadPoolExecutor(max_workers=len(parallel_tasks)) as executor:
                    # Submit all tasks
                    future_to_task = {executor.submit(task_func): task_name for task_name, task_func in parallel_tasks}

                    # Process results as they complete
                    for future in as_completed(future_to_task):
                        task_name = future_to_task[future]
                        try:
                            result = future.result()
                            if task_name == 'steps_translation' and result is not None:
                                steps_df = result  # Update steps_df with the result from translate_steps_for_a_case
                            log_message(f"✅ {task_name} completed successfully for case {docket_number}", level='INFO')
                        except Exception as task_err:
                            log_message(f"❌ Error in {task_name} for case {docket_number}: {task_err}", level='ERROR')

                # Close NAS connection if it was created
                if 'nas' in locals():
                    try:
                        nas.close()
                    except Exception as close_err:
                        log_message(f"❌ Error closing NAS connection: {close_err}", level='ERROR')
            else:
                log_message(f"ℹ️ No parallel tasks to execute for case {docket_number}", level='INFO')

        except Exception as e:
            log_message(f"❌ Unhandled error in parallel tasks for case {docket_number}: {e}\n{traceback.format_exc()}", level='ERROR')
        finally:
            _can_upload = options.get('upload_files', False) and idx is not None and idx in cases_df.index
            _steps_df_available_for_ai = 'steps_df' in locals() and steps_df is not None and not steps_df.empty
            _step_translation_called_str = 'Yes' if options.get('run_step_translation', False) and _steps_df_available_for_ai else ('No/Skipped' if options.get('run_step_translation', False) else 'No')

            span_parallel_tasks.end(output={
                "NASUploadAttempted": 'Yes' if _can_upload and any(name == 'nas_upload' for name, _ in parallel_tasks) else 'No/Skipped',
                "COSUploadAttempted": 'Yes' if _can_upload and any(name == 'cos_upload' for name, _ in parallel_tasks) else 'No/Skipped',
                "SummaryTranslationCalled": 'Yes' if options.get('run_summary_translation', False) else 'No',
                "StepTranslationCalled": _step_translation_called_str
            })


        # 7. Final Database Updates (Status, Stats)
        span_final_db_updates = case_procesor_trace.span(name="final_db_updates",
            input={"Case": docket_number, "InitialFileStatus": cases_df.loc[idx].get('file_status', 'Unknown') if idx is not None and idx in cases_df.index else 'N/A', "SaveToDBOption": options.get('save_to_db', True)}
        )
        try:
            log_message(f"💾 Finalizing processing status for case: {docket_number}", level='INFO')
            current_status = str(cases_df.loc[idx].get('file_status', 'Unknown'))
            final_status = current_status # Initialize final_status for this block

            if "Failed" not in final_status and "Admin Closed" not in final_status:
                all_goals_met = ip_manager.are_all_relevant_goals_met()
                if all_goals_met:
                    final_status = "IP Goals Met"
                else:
                    final_status = "Completed - Goals Unmet"
                    log_message(f"   🔥  Final Status Check: Not all relevant IP goals were met.", level='INFO')
                    unmet_types = [ip_type for ip_type in ip_manager.iptypes if ip_manager._state[ip_type]['is_relevant'] and not ip_manager.is_goal_met(ip_type)]
                    log_message(f"      Unmet IP types: {unmet_types}", level='DEBUG')

                cases_df.loc[idx, 'file_status'] = final_status
                log_message(f"📊 Set final status for case {docket_number} to '{final_status}'", level='INFO')

                if options.get('save_to_db', True):
                    log_message(f"💾 Saving updates to database for case {docket_number}...", level='INFO')
                    try:
                        # Save the ip_manager state to the images_status field
                        final_ip_manager_state_dict = ip_manager.export_state()
                        cases_df.at[idx, 'images_status']['ip_manager_state'] = final_ip_manager_state_dict
                        log_message(f"   Saved ip_manager state.", level='INFO')

                        cases_df.loc[idx, 'images_status']['number_of_pdfs'] = int(sum(steps_df['files_downloaded']))

                        # Save the updated case row
                        insert_and_update_df_to_GZ_batch(cases_df.loc[[idx]], 'tb_case', 'id')
                        update_feather_file('tb_case', cases_df)
                        log_message(f"   Saved case row for case {docket_number} to tb_case", level='DEBUG')

                        # --- Prepare steps_df_to_save ---
                        if steps_df is not None and not steps_df.empty:
                            # Select necessary columns from the working steps_df
                            steps_df = steps_df[[
                                'case_id', 'step_nb', 'step_date_filed', 'proceeding_text',
                                'proceeding_text_cn', 'priority_name',
                                'files_number', 'files_downloaded', 'files_failed'
                            ]].copy()  # Why .copy()? steps_df[[...]] creates a copy, but that is not obvious. We add .copy() to make it clear (and avoid a warning)

                            # Save the prepared DataFrame
                            steps_df = insert_and_update_df_to_GZ_id(steps_df, 'tb_case_steps', 'case_id', 'step_nb')
                            log_message(f"   Saved {len(steps_df)} steps to tb_case_steps.gz", level='DEBUG')
                        else:
                            log_message(f"   Skipping steps save: No steps data to save.", level='DEBUG')
                        # ---------------------------------
                        log_message(f"💾 GZ saves complete for case {docket_number}.", level='INFO')
                    except Exception as db_save_err:
                        log_message(f"❌ Error saving updates to GZ for case {docket_number}: {db_save_err}", level='ERROR')
                        # Decide if this should change the final status or success
                else:
                    log_message(f"ℹ️ Skipping database save (save_to_db=False).", level='INFO')
        finally: # for span_final_db_updates
            _final_status_for_span = final_status if 'final_status' in locals() else 'N/A'
            _case_row_saved_str = 'No/Skipped'
            if options.get('save_to_db', True) and 'final_status' in locals() and "Failed" not in final_status and "Error" not in final_status:
                _case_row_saved_str = 'Yes'
            _steps_saved_count = 0
            if options.get('save_to_db', True) and 'steps_df' in locals() and steps_df is not None and not steps_df.empty and 'final_status' in locals() and "Failed" not in final_status and "Error" not in final_status:
                 _steps_saved_count = len(steps_df) # Assuming steps_df is the one to be saved.
            span_final_db_updates.end(output={"FinalStatusSet": _final_status_for_span, "CaseRowSaved": _case_row_saved_str, "StepsSavedCount": _steps_saved_count})

        # Final reporting and return
        log_message(f"✅✅✅ Successfully completed processing flow for case: {docket_number}. Final Status: {final_status}", level='INFO')

        # Save LexisNexis statistics to database
        try:
            # Create a row for the statistics DataFrame
            stats_row = {'event_date': datetime.now().date(), 'case_id': cases_df.at[idx, 'id'],
                'pacer_refresh': pacer_refresh, 'pacer_file': pacer_file, 'total_file': total_file, 'account': os.environ["LNusername"]}

            # Add the row to the DataFrame
            df_lexis_stats = pd.DataFrame([stats_row])

            # Save to database
            insert_and_update_df_to_GZ_id(df_lexis_stats, "lexis", "event_date", "case_id")
        except Exception as stats_err:
            log_message(f"❌ Error saving LexisNexis statistics: {stats_err}", level='ERROR')

        final_success_status = "Failed" not in final_status and "Error" not in final_status
        case_procesor_trace.update(output={"OverallSuccess": final_success_status, "FinalCaseStatus": final_status, "CaseProcessed": docket_number, "StepsProcessed": cases_df.at[idx, 'images_status']['steps_processed'] if idx is not None and idx in cases_df.index and 'steps_processed' in cases_df.at[idx, 'images_status'] else [], "IPGoalsAfterStep": {"TM": ip_manager.is_goal_met('trademark'), "CR": ip_manager.is_goal_met('copyright'), "PT": ip_manager.is_goal_met('patent')}, "MissingStatus": ip_manager.get_missing_status()})
        return final_success_status, cases_df.loc[[idx]]

    except Exception as e: # Main exception handler for this function
        log_message(f"💥 Unhandled exception during processing part 2 for case {docket_number}: {e}\n{traceback.format_exc()}", level='CRITICAL')
        # final_status might not be updated if error is early. Use the one initialized or updated.
        _current_final_status = final_status if 'final_status' in locals() else f"Failed: Critical Part 2 - {str(e)[:50]}"

        if case_procesor_trace: # Check if trace object exists
            case_procesor_trace.update(output={"OverallSuccess": False, "FinalCaseStatus": _current_final_status})

        # Update final_status to reflect this critical error for DataFrame update
        final_status = f"Failed: Critical Part 2 - {str(e)[:100]}" # Ensure final_status is updated

        # Even in case of error, try to save statistics if we have any
        if 'pacer_refresh' in locals() and 'pacer_file' in locals() and 'total_file' in locals():
            try:
                # Create a row for the statistics DataFrame
                stats_row = {'event_date': datetime.now().date(), 'case_id': cases_df.at[idx, 'id'],
                    'pacer_refresh': pacer_refresh, 'pacer_file': pacer_file, 'total_file': total_file, 'account': os.environ["LNusername"]}

                # Add the row to the DataFrame
                df_lexis_stats = pd.DataFrame([stats_row])

                # Save to database
                insert_and_update_df_to_GZ_id(df_lexis_stats, "lexis", "event_date", "case_id")
            except Exception as stats_err:
                log_message(f"❌ Error saving LexisNexis statistics during error handling: {stats_err}", level='ERROR')

        df_to_return_on_error = pd.DataFrame()
        if idx is not None and idx in cases_df.index:
            try:
                error_str = final_status[:255]
                cases_df.loc[idx, 'file_status'] = error_str
                df_to_return_on_error = cases_df.loc[[idx]]
            except Exception as update_err:
                log_message(f"❌ Failed to update error status in DataFrame for case {docket_number} at index {idx}: {update_err}", level='ERROR')
        return False, df_to_return_on_error