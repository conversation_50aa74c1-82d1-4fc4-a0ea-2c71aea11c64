# IP/Trademarks/trademark_image.py

import os
import io
import asyncio
import aiohttp
import logging
from PIL import Image
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("trademark_image.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Standard User-Agent to mimic a browser
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

async def download_and_save_image(
    session: aiohttp.ClientSession,
    serial_number: str,
    save_dir: str,
    final_dir: str,
    semaphore: asyncio.Semaphore, # Added semaphore parameter
    max_retries=5,
    timeout=30
) -> bool:
    """
    Downloads a trademark image from USPTO, saves it, and returns success status,
    respecting a semaphore for concurrency control.

    Args:
        session (aiohttp.ClientSession): The shared client session.
        serial_number (str): Serial number of the trademark.
        save_dir (str): Base directory to save the image.
        semaphore (asyncio.Semaphore): Semaphore to limit concurrent downloads.
        max_retries (int): Maximum number of retry attempts.
        timeout (int): Request timeout in seconds.

    Returns:
        bool: True if download and save were successful, False otherwise.
    """
    if not serial_number:
        logger.warning("Attempted to download image with empty serial number.")
        return False
    
    # Determine directory structure (last 2 digits, then 2 digits before that)
    if len(serial_number) >= 4:
        xx = serial_number[-2:]
        yy = serial_number[-4:-2]
    elif len(serial_number) > 0: # Handle short serial numbers
            xx = serial_number.zfill(2)[-2:] # Pad with zeros if needed
            yy = "00"
    else: # Should not happen due to initial check, but defensive
        logger.error("Cannot determine directory structure for empty serial number.")
        return False
    
    final_sub_dir = os.path.join(final_dir, xx, yy)
    os.makedirs(final_sub_dir, exist_ok=True)
    if os.path.exists(os.path.join(final_sub_dir, f"{serial_number}.webp")):
        # logger.info(f"Image for serial number {serial_number} already exists. Skipping download.")
        return True

    image_url = f"https://tsdr.uspto.gov/img/{serial_number}/large"
    image_data = None

    async with semaphore: # Acquire semaphore before proceeding
        logger.debug(f"Semaphore acquired for {serial_number}. Starting download attempt.")

        for attempt in range(max_retries):
            try:
                async with session.get(image_url, headers=HEADERS, timeout=timeout) as response:
                    if response.status == 200:
                        image_data = await response.read()
                        if attempt != 0:
                            logger.info(f"Successfully downloaded image data for {serial_number} from {image_url} on attempt {attempt + 1}/{max_retries}")
                        # logger.debug(f"Successfully downloaded image data for {serial_number} from {image_url}")
                        break # Exit retry loop on success
                    elif response.status == 403:
                        logger.error(f"Access Forbidden (403) for URL: {image_url}. Check User-Agent or API restrictions.")
                        # No retry on 403, likely a persistent issue
                        return False
                    elif response.status == 404:
                        logger.warning(f"Image not found (404) for serial number {serial_number} at URL: {image_url}")
                        # No retry on 404
                        return False
                    else:
                        logger.warning(f"Unexpected status code {response.status} for URL: {image_url} (Attempt {attempt + 1}/{max_retries})")
                        if attempt == max_retries - 1:
                            return False # Failed after all retries

            except asyncio.TimeoutError:
                await asyncio.sleep(5)
                if attempt == max_retries - 1:
                    logger.warning(f"Timeout occurred on attempt {attempt + 1}/{max_retries} for URL {image_url}")
                    return False # Failed after all retries

            except aiohttp.ClientError as e:
                logger.error(f"Client error downloading image for {serial_number} from {image_url} (Attempt {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return False # Failed after all retries

            except Exception as e:
                logger.error(f"Unexpected error downloading image for {serial_number} from {image_url} (Attempt {attempt + 1}/{max_retries}): {e}", exc_info=True)
                if attempt == max_retries - 1:
                    return False # Failed after all retries

                # Wait before retrying (exponential backoff) - still inside the semaphore lock
                if attempt < max_retries - 1:
                    await asyncio.sleep(1 * (2 ** attempt)) # 1s, 2s, 4s...

    # --- End of semaphore block ---

    # Check if download was successful (image_data is not None)
    if not image_data:
        # Logging is handled within the loop for specific errors (404, timeout, etc.)
        # Add a general failure log if it exited loop without success or specific error log
        logger.warning(f"Failed to download image data for {serial_number} after {max_retries} attempts (outside semaphore).")
        return False

    try:
        # Create directory if it doesn't exist
        image_sub_dir = os.path.join(save_dir, xx, yy)
        os.makedirs(image_sub_dir, exist_ok=True)

        # Save image
        image_path = os.path.join(image_sub_dir, f"{serial_number}.webp")
        image = Image.open(io.BytesIO(image_data))
        # Ensure image is in RGB mode before saving as WEBP if it's RGBA or P
        if image.mode in ('RGBA', 'P'):
             image = image.convert('RGB')
        image.save(image_path, "WEBP", quality=85) # Added quality setting

        # logger.info(f"Successfully saved image for serial number {serial_number} to {image_path}")
        return True

    except Exception as e:
        logger.error(f"Error saving image for serial number {serial_number}: {str(e)}", exc_info=True)
        return False
